import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment';
import { Event_Actions } from 'src/event-listeners/actions';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import Category from 'src/modules/categories/categories.entity';
import Client from 'src/modules/clients/entity/client.entity';
import Label from 'src/modules/labels/label.entity';
import RecurringProfile from 'src/modules/recurring/entity/recurring-profile.entity';
import { Service } from 'src/modules/services/entities/service.entity';
import { User, UserType } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, getManager, In, Like } from 'typeorm';
import { AddRemkarDto } from '../dto/add-remark.dto';
import CreateTaskDto from '../dto/create-task.dto';
import FindTasksQuery from '../dto/find-query.dto';
import FindUserTasksDto from '../dto/find-user-tasks.dto';
import * as xlsx from 'xlsx';
import {
  DateFilterKeys,
  DateFilters,
  DateKeys,
  IFilterByDate,
  PaymentStatusEnum,
  ProformaTaskStatus,
  RecurringFrequency,
  ServiceType,
  TaskRecurringStatus,
  TaskStatusEnum,
  TaskType,
  UpdateStatusBody,
} from '../dto/types';
import UpdateTaskDto from '../dto/update-task.dto';
import Checklist from '../entity/checklist.entity';
import Milestone from '../entity/milestone.entity';
import StageOfWork from '../entity/stage-of-work.entity';
import TaskStatus from '../entity/task-status.entity';
import Task from '../entity/task.entity';
import { Permissions } from '../permission';
import LogHour from 'src/modules/log-hours/log-hour.entity';
import { dateFormation } from 'src/utils/datesFormation';
import ApprovalProcedures from 'src/modules/atm-qtm-approval/entities/approval-procedures.entity';
import axios from 'axios';
import ChecklistItem from '../entity/checklist-item.entity';
import BudgetedHours, {
  BudgetedHourStatus,
} from 'src/modules/budgeted-hours/budgeted-hours.entity';
import { formatDate, getTitle } from 'src/utils';
import { BillableType } from 'src/modules/reports/dto/get-employee-log-hours-report';
import { getBulkTasks } from '../dto/get-bulk-tasks.dto';
import UdinTask, { UdinTaskStatus, userType } from 'src/modules/udin-task/udin-task.entity';
import ClientGroup, { UserStatus } from 'src/modules/client-group/client-group.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { type } from 'os';
import * as ExcelJS from 'exceljs';
import UpdateRemarksDto from '../dto/update-remarks.dto';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { ReturnsData } from 'src/modules/gstr-register/entity/returns-data.entity';
import { PromiseService } from 'src/modules/gstr-register/services/promise.service';
import { RegistrationType } from 'src/modules/gstr-register/entity/gstr-register.entity';
import FindReturnsDto from 'src/modules/gstr-register/dto/find-returns.dto';

interface ICreateTask {
  userId: number;
  client: Client;
  data: CreateTaskDto;
  user: User;
  taskLeader: User[];
  members: User[];
  labels: Label[];
  service: Service;
  category: Category;
  subCategory: Category;
  appHier: ApprovalProcedures;
  clientGroup: ClientGroup;
}

export enum TimerStatus {
  STARTED = 'started',
  STOPPED = 'stopped',
}

export const months = [
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
  'January',
  'February',
  'March',
];

export const quarters = [
  'Q1 (April - June)',
  'Q2 (July - September)',
  'Q3 (October - December)',
  'Q4 (January - March)',
];

export const halfYears = ['H1 (April - September)', 'H2 (October - March)'];

@Injectable()
export class TasksService {
  constructor(private eventEmitter: EventEmitter2, private promiseService: PromiseService) {}

  async create(userId: number, data: CreateTaskDto) {
    let clients = await Client.find({ where: { id: In(data.client) } });
    let clientGroup = null;
    if (data.clientGroup) {
      clientGroup = await ClientGroup.findOne({ where: { id: data.clientGroup['value'] } });
    }
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let taskLeader = await User.find({
      where: { id: In(data.taskLeader), type: UserType.ORGANIZATION },
    });
    let members = await User.find({ where: { id: In(data.members), type: UserType.ORGANIZATION } });
    let labels = await Label.find({ where: { id: In(data.labels) } });
    let category = await Category.findOne({ where: { id: data.category } });
    let sCategory = await Category.findOne({ where: { id: data.subCategory } });
    let service: Service;
    let appHier;

    if (data.serviceType === ServiceType.STANDARD) {
      let result = await createQueryBuilder(Service, 'service')
        .leftJoinAndSelect('service.category', 'category')
        .leftJoinAndSelect('service.subCategory', 'subCategory')
        .leftJoinAndSelect('service.checklists', 'checklists')
        .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
        .leftJoinAndSelect('service.subTasks', 'subTasks')
        .leftJoinAndSelect('service.milestones', 'milestones')
        .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
        .leftJoinAndSelect('stageOfWorks.storage', 'storage')
        .where('service.id = :id', { id: data.service })
        .getOne();
      service = result;
    }

    let existingNumber = await createQueryBuilder(Task, 'task')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.service', 'service')
      .where('task.financialYear = :financialYear', { financialYear: data.financialYear });

    if (service) {
      existingNumber.andWhere('service.id = :id', { id: data.service });
    } else {
      existingNumber.andWhere('task.name LIKE :searchString', { searchString: `%${data.name}%` });
    }

    if (data?.client?.length) {
      existingNumber.andWhere('client.id IN (:...clientId)', { clientId: data.client });
    }

    if (data?.clientGroup) {
      existingNumber.andWhere('clientGroup.id = :clientGroupId', {
        clientGroupId: data.clientGroup['value'],
      });
    }

    const taskCheck = await existingNumber.getOne();

    if (taskCheck && data?.taskCreationCheck) {
      throw new BadRequestException('Task Already Exists');
    }

    if (data.approvalHierarchy) {
      appHier = data.approvalHierarchy;
    }

    const args = {
      userId: userId,
      data: data,
      user: user,
      taskLeader: taskLeader,
      members: members,
      labels: labels,
      category: category,
      subCategory: sCategory,
      service: service,
      appHier: appHier,
    };
    let task = {};
    try {
      if (clients.length) {
        for (let client of clients) {
          if (data.taskType === TaskType.RECURRING) {
            await this.createRecurringTask({ ...args, client, clientGroup });
          }

          if (data.taskType === TaskType.NON_RECURRING) {
            task = await this.createNonRecurringTask({ ...args, client, clientGroup });
          }
        }
      } else {
        let client = null;
        if (data.taskType === TaskType.RECURRING) {
          await this.createRecurringTask({ ...args, client, clientGroup });
        }

        if (data.taskType === TaskType.NON_RECURRING) {
          task = await this.createNonRecurringTask({ ...args, client, clientGroup });
        }
      }
      //Todo: when task created send espo email
      this.eventEmitter.emit(Event_Actions.TASK_CREATED, { ...args, clients });
      return task;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException('Internal server error');
    }
  }

  async createNonRecurringTask(props: ICreateTask) {
    const { user, client, labels, members, taskLeader, ...rem } = props;
    const { data, category, subCategory, service, appHier, clientGroup } = rem;

    let task = new Task();
    task.client = client;
    task.clientGroup = clientGroup;
    task.user = user;
    task.dueDate = data.dueDate;
    task.priority = data.priority;
    task.feeType = data.feeType;
    task.feeAmount = data?.billable ? data.feeAmount : 0;
    task.budgetedhours = data.budgetedhours;
    task.labels = labels;
    task.members = members;
    task.taskLeader = taskLeader;
    task.organization = user.organization;
    task.taskStartDate = data.startDate;
    task.financialYear = data.financialYear;
    task.expectedCompletionDate = data.expectedCompletionDate;
    task.name = data.name.trim();
    task.description = data.description;
    task.category = category;
    task.subCategory = subCategory;
    task.billable = data.parentTask ? false : data.billable;
    task.budgetedhours = data?.budgetedHoursInSeconds;

    if (moment(task.taskStartDate) > moment()) {
      task.recurringStatus = TaskRecurringStatus.PENDING;
    } else {
      task.recurringStatus = TaskRecurringStatus.CREATED;
      task.createdDate = moment().toDate();
    }
    if (data.parentTask) {
      const subtaskParent = await Task.findOne({
        where: {
          id: data.parentTask,
        },
      });
      task.parentTask = subtaskParent;
      task.recurring = false;
    }

    if (service) {
      task.name = service.name;
      task.category = service.category;
      task.subCategory = service.subCategory;
      task.service = service;

      if (task.recurringStatus === TaskRecurringStatus.CREATED) {
        task.description = service.description;
        task.checklists = service.checklists.map((checklist) => {
          let result = new Checklist();
          delete checklist.id;
          result.name = checklist.name;
          result['userId'] = user.id;
          result.checklistItems = checklist.checklistItems.map((item) => {
            let newItem = new ChecklistItem();
            delete item.id;
            newItem.name = item.name;
            newItem.description = item.description;
            newItem['userId'] = user.id;
            return newItem;
          });
          return result;
        });

        task.milestones = service.milestones.map((milestone) => {
          let result = new Milestone();
          delete milestone.id;
          Object.assign(result, milestone);
          return result;
        });
        task.stageOfWorks = service.stageOfWorks.map((stageOfWork) => {
          let result = new StageOfWork();
          delete stageOfWork.id;
          Object.assign(result, stageOfWork);
          return result;
        });
      }
    }

    if (appHier) {
      // let approvalAprocedures = await ApprovalProcedures.findOne({ where: { id: appHier } })
      const approval = await ApprovalProcedures.findOne({
        where: { id: appHier, organization: user.organization.id },
        relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
        select: ['id'],
      });

      task.approvalProcedures = approval;
      try {
        const data = JSON.stringify({
          processKey: 'genericApprovalProcess',
          metaData: {
            typeOfApproval: 'ATOM_TASK',
            approvalProcessId: `Level${approval?.approval?.approvalLevels.length}ApprovalProcess`,
          },
        });

        let config: any = {
          method: 'post',
          maxBodyLength: Infinity,
          url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
          headers: {
            'Content-Type': 'application/json',
          },
          data: data,
        };

        await axios.request(config).then(async (response) => {
          const processInstanceId = response?.data?.processInstanceId;
          if (processInstanceId)
            task.approvalStatus = [
              {
                status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
                completed: false,
              },
            ];
          task.processInstanceId = processInstanceId;
          this.approvalProcess(processInstanceId, approval);
        });
      } catch (err) {
        console.log(err);
      }
    }

    task.restore = task.status;
    task['userId'] = user.id;
    task.isUdin = data?.isUdin ? true : false;

    await task.save();

    for (let item of data?.userBudgetedHours) {
      let userData = await User.findOne({ where: { id: item.id }, relations: ['organization'] });

      let budgetedHours = new BudgetedHours();
      budgetedHours.status = BudgetedHourStatus.ACTIVE;
      budgetedHours.budgetedHours = item['userBudgetedHoursInSeconds'];
      budgetedHours.organization = user.organization;
      budgetedHours.task = task;
      budgetedHours.user = userData;
      await budgetedHours.save();
    }

    if (data?.isUdin) {
      let udintask = new UdinTask();
      udintask.task = task;
      udintask.udinTaskStatus = UdinTaskStatus.ACTIVE;
      udintask.userType = userType.ORGANIZATION;
      udintask.organization = user.organization;
      await udintask.save();
    }

    if (task.recurringStatus === TaskRecurringStatus.CREATED) {
      let taskStatus = new TaskStatus();
      taskStatus.restore = TaskStatusEnum.TODO;
      taskStatus.status = TaskStatusEnum.TODO;
      taskStatus.task = task;
      taskStatus.user = user;

      await taskStatus.save();

      let activity = new Activity();
      activity.action = Event_Actions.TASK_CREATED;
      activity.actorId = user.id;
      activity.type = task.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
      activity.typeId = task.client ? task?.client?.id : task?.clientGroup?.id;

      activity.remarks = `"<b>${task.taskNumber}</b>" - "${task?.name}" Task created by ${user.fullName}`;
      await activity.save();

      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.TASK_CREATED;
      taskactivity.actorId = user.id;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = task.id;

      taskactivity.remarks = `"${task.taskNumber}" - "${task?.name}" Task created by ${user.fullName}`;
      await taskactivity.save();
    }

    if (task.recurringStatus === TaskRecurringStatus.PENDING) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.TASK_SCHEDULED;
      taskactivity.actorId = user.id;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = task.id;

      taskactivity.remarks = `"${task?.name}" Scheduled by ${user.fullName} on ${moment(
        task.taskStartDate,
      ).format('DD-MM-YYYY')}`;
      await taskactivity.save();
    }

    return task;
  }

  async approvalProcess(id: string, approvalData: ApprovalProcedures) {
    try {
      const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
      let data: any = {
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(url, data);
      const { approval } = approvalData;
      const { approvalLevels } = approval;
      let assignApprovalTasks: any[] = [];
      assignApprovalTasks = response?.data?.tasks
        .filter((item: any) => item.name !== 'holdTillDocumentIsSubmitted')
        .map((item: any) => {
          let levelNumber = parseInt(item.name.slice(-1));
          const foundUser = approvalLevels.find((level) => level.level === levelNumber);
          const { user } = foundUser;
          const { id } = user;
          return `${process.env.CAMUNDA_URL}/vider/quantum/api/task/${item.id}/assign/${id}`;
        });

      const makeApiCall = async (url) => {
        try {
          let config: any = {
            method: 'put',
            maxBodyLength: Infinity,
            headers: {
              'Content-Type': 'application/json',
            },
          };

          const response = await axios.put(url, config);
          return response.data;
        } catch (error) {
          console.error('Error for', url, ':', error);
          throw error;
        }
      };

      const apiPromises = assignApprovalTasks.map((endpoint) => makeApiCall(endpoint));

      Promise.all(apiPromises)
        .then((apiResponses) => {})
        .catch((error) => {
          console.error('One or more API calls failed:', error);
        });
    } catch (err) {
      console.log(err);
    }
  }

  async createRecurringTask(props: ICreateTask) {
    const { user, client, labels, members, taskLeader, ...rem } = props;
    const { data, category, subCategory, appHier, service, clientGroup } = rem;
    function generateRRTId(id: number) {
      if (id < 10000) {
        return 'RRT' + id.toString().padStart(4, '0');
      }
      return 'RRT' + id;
    }
    let recurringCount = await createQueryBuilder(Task, 'task')
      .leftJoin('task.organization', 'organization')
      .where('organization.id = :id', { id: user.organization.id })
      .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%RRT%' })
      .getCount();

    let recurringProfile = new RecurringProfile();
    recurringProfile.name = service?.name || data.name;
    recurringProfile.financialYear = data.financialYear;
    recurringProfile.frequency = data.frequency;
    recurringProfile.user = user;
    recurringProfile.client = client;
    recurringProfile.clientGroup = clientGroup;

    let tasks: Task[] = [];
    let pendingTasks: Task[] = [];

    const splitDataFinancialYear = data.financialYear.split('-');
    const oldDataYear = Number(splitDataFinancialYear[0]);
    const newDataYear = Number(splitDataFinancialYear[1]);
    const oldDataFYear = String(Number(splitDataFinancialYear[0]) + 1);
    const newDataFYear = String(Number(splitDataFinancialYear[1]) + 1);
    const newDataFinancialYear = oldDataFYear + '-' + newDataFYear;

    const quarterlyOldYears = quarters.map((item) =>
      item.includes('Q4') ? item + ' ' + oldDataFYear : item + ' ' + oldDataYear,
    );
    const newQuarterlyYears = quarters.map((item) =>
      item.includes('Q4') ? item + ' ' + newDataFYear : item + ' ' + newDataYear,
    );

    const halfYearlyOldYears = halfYears.map((item) =>
      item.includes('H2') ? item + ' ' + oldDataFYear : item + ' ' + oldDataYear,
    );
    const newHalfyearlyYears = halfYears.map((item) =>
      item.includes('H2') ? item + ' ' + newDataFYear : item + ' ' + newDataYear,
    );

    const monthlyOldYears = months.map((item) =>
      item.includes('January') || item.includes('February') || item.includes('March')
        ? item + ' ' + oldDataFYear
        : item + ' ' + oldDataYear,
    );
    const newMonthlyYears = months.map((item) =>
      item.includes('January') || item.includes('February') || item.includes('March')
        ? item + ' ' + newDataFYear
        : item + ' ' + newDataYear,
    );

    for (let index = 0; index < data.dates.length; index++) {
      const date = data.dates[index];
      let nameExt;
      let task = new Task();
      if (data.frequency === RecurringFrequency.QUARTERLY) {
        if (quarterlyOldYears.includes(date.period)) {
          task.financialYear = data.financialYear;
          nameExt = ' - ' + data.financialYear + ' - ' + date.period;
        } else if (newQuarterlyYears.includes(date.period)) {
          task.financialYear = newDataFinancialYear;
          nameExt = ' - ' + newDataFinancialYear + ' - ' + date.period;
        }
      } else if (data.frequency === RecurringFrequency.HALF_YEARLY) {
        if (halfYearlyOldYears.includes(date.period)) {
          task.financialYear = data.financialYear;
          nameExt = ' - ' + data.financialYear + ' - ' + date.period;
        } else if (newHalfyearlyYears.includes(date.period)) {
          task.financialYear = newDataFinancialYear;
          nameExt = ' - ' + newDataFinancialYear + ' - ' + date.period;
        }
      } else if (data.frequency === RecurringFrequency.MONTHLY) {
        if (monthlyOldYears.includes(date.period)) {
          task.financialYear = data.financialYear;
          nameExt = ' - ' + data.financialYear + ' - ' + date.period;
        } else if (newMonthlyYears.includes(date.period)) {
          task.financialYear = newDataFinancialYear;
          nameExt = ' - ' + newDataFinancialYear + ' - ' + date.period;
        }
      } else if (data.frequency === RecurringFrequency.YEARLY) {
        task.financialYear = data.financialYear;
        nameExt = ' - ' + data.financialYear + ' - ' + 'Yearly';
      } else {
        task.financialYear = data.financialYear;
        nameExt = ' - ' + data.financialYear + ' - ' + date.period;
      }

      task.name = data.name.trim() + nameExt;
      task.description = data.description;
      task.category = category;
      task.subCategory = subCategory;
      task.client = client;
      task.clientGroup = clientGroup;
      task.user = user;
      task.priority = data.priority;
      task.feeType = data.feeType;
      task.feeAmount = data.feeAmount;
      task.budgetedhours = data.budgetedhours;
      // task.bhallocation = JSON.stringify(data.bhallocation);
      task.labels = labels;
      task.members = members;
      task.taskLeader = taskLeader;
      task.organization = user.organization;
      task.recurring = true;
      task.recurringProfile = recurringProfile;
      task.frequency = data.frequency;
      task.taskStartDate = date.startDate;
      task.dueDate = date.dueDate;
      task.expectedCompletionDate = date.expectedCompletionDate;
      task.recurringStatus = TaskRecurringStatus.PENDING;
      task.restore = TaskStatusEnum.TODO;
      task.billable = data.billable;
      task.budgetedhours = data?.budgetedHoursInSeconds;
      let today = moment().format('YYYY-MM-DD');
      if (appHier) {
        const approval = await ApprovalProcedures.findOne({
          where: { id: appHier, organization: user.organization.id },
          relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
          select: ['id'],
        });
        task.approvalProcedures = approval;
      }

      if (date.startDate <= today) {
        if (appHier) {
          const approval = await ApprovalProcedures.findOne({
            where: { id: appHier, organization: user.organization.id },
            relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
            select: ['id'],
          });
          // task.approvalProcedures = approval;
          try {
            const data = JSON.stringify({
              processKey: 'genericApprovalProcess',
              metaData: {
                typeOfApproval: 'ATOM_TASK',
                approvalProcessId: `Level${approval?.approval?.approvalLevels.length}ApprovalProcess`,
              },
            });

            let config: any = {
              method: 'post',
              maxBodyLength: Infinity,
              url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
              headers: {
                'Content-Type': 'application/json',
              },
              data: data,
            };

            await axios.request(config).then(async (response) => {
              const processInstanceId = response?.data?.processInstanceId;

              if (processInstanceId)
                task.approvalStatus = [
                  {
                    status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
                    completed: false,
                  },
                ];
              task.processInstanceId = processInstanceId;
              this.approvalProcess(processInstanceId, approval);
            });
          } catch (err) {
            console.log(err);
          }
        }
        task.recurringStatus = TaskRecurringStatus.CREATED;
        task.taskNumber = generateRRTId(recurringCount + index + 1);
        task.createdDate = moment().toDate();
      }
      if (service) {
        task.name = service.name + nameExt;
        task.category = service.category;
        task.subCategory = service.subCategory;
        task.service = service;
      }

      if (service && task.recurringStatus === TaskRecurringStatus.CREATED) {
        task.description = service.description;
        task.checklists = service.checklists.map((checklist) => {
          let result = new Checklist();
          delete checklist.id;
          result.name = checklist.name;
          result.task = task;
          result['userId'] = user.id;
          result.checklistItems = checklist.checklistItems.map((item) => {
            let newItem = new ChecklistItem();
            delete item.id;
            newItem.name = item.name;
            newItem.description = item.description;
            newItem['userId'] = user.id;
            return newItem;
          });
          return result;
        });

        task.milestones = service.milestones.map((milestone) => {
          let result = new Milestone();
          delete milestone.id;
          Object.assign(result, milestone);
          return result;
        });

        task.stageOfWorks = service.stageOfWorks.map((stageOfWork) => {
          let result = new StageOfWork();
          delete stageOfWork.id;
          Object.assign(result, stageOfWork);
          return result;
        });
      }

      tasks.push(task);
      task['userId'] = user.id;
      task.isUdin = data?.isUdin ? true : false;

      await Task.save(task);

      if (task.recurringStatus === TaskRecurringStatus.CREATED) {
        let activity = new Activity();
        activity.action = Event_Actions.TASK_CREATED;
        activity.actorId = user.id;
        activity.type = task?.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        activity.typeId = task?.client ? client?.id : clientGroup?.id;

        activity.remarks = `"${task.taskNumber}" - "${task?.name}" Task created by ${user.fullName}`;
        await activity.save();

        let taskStatus = new TaskStatus();
        taskStatus.restore = TaskStatusEnum.TODO;
        taskStatus.status = TaskStatusEnum.TODO;
        taskStatus.task = task;
        taskStatus.user = user;
        await taskStatus.save();

        let taskactivity = new Activity();
        taskactivity.action = Event_Actions.TASK_CREATED;
        taskactivity.actorId = user.id;
        taskactivity.type = ActivityType.TASK;
        taskactivity.typeId = task.id;

        taskactivity.remarks = `"${task.taskNumber}" - "${task?.name}" Task created by ${user.fullName}`;
        await taskactivity.save();
      }

      if (task.recurringStatus === TaskRecurringStatus.PENDING) {
        let taskactivity = new Activity();
        taskactivity.action = Event_Actions.TASK_SCHEDULED;
        taskactivity.actorId = user.id;
        taskactivity.type = ActivityType.TASK;
        taskactivity.typeId = task.id;

        taskactivity.remarks = `"${task?.name}" Scheduled by ${user.fullName} on ${moment(
          task.taskStartDate,
        ).format('DD-MM-YYYY')}`;
        await taskactivity.save();
      }

      for (let item of data?.userBudgetedHours) {
        let userData = await User.findOne({ where: { id: item.id }, relations: ['organization'] });

        let budgetedHours = new BudgetedHours();
        budgetedHours.status = BudgetedHourStatus.ACTIVE;
        budgetedHours.budgetedHours = item['userBudgetedHoursInSeconds'];
        budgetedHours.organization = user?.organization;
        budgetedHours.task = task;
        budgetedHours.user = userData;
        await budgetedHours.save();
      }

      if (data?.isUdin) {
        let udintask = new UdinTask();
        udintask.task = task;
        udintask.udinTaskStatus = UdinTaskStatus.ACTIVE;
        udintask.userType = userType.ORGANIZATION;
        udintask.organization = user.organization;
        udintask.recurringProfile = recurringProfile;
        await udintask.save();
      }
    }

    const taskRecurring = async (task) => {
      if (task.recurring === true) {
        if (task.frequency !== 'custom') {
          const recurringProfile = task.recurringProfile;
          const user = task.user;

          let nameExt = '';
          let StartDate = '';
          let dueDate = '';
          let expectedCompletionDate = '';
          let taskName = '';
          const splitYear = task.financialYear?.split('-');
          const oldYear = String(Number(splitYear[0]) + 1);
          const newYear = String(Number(splitYear[1]) + 1);
          const newFinancialYear = oldYear + '-' + newYear;

          const getStartDate = () => {
            const startDateObj = moment.utc(task.taskStartDate);
            const startDateObjAdd = startDateObj.add(1, 'year');
            StartDate = startDateObjAdd.format('YYYY-MM-DD');
          };

          const getDueDate = () => {
            const dueDateObj = moment.utc(task.dueDate);
            const dueDateObjAdd = dueDateObj.add(1, 'year');
            dueDate = dueDateObjAdd.format('YYYY-MM-DD');
          };

          const getExpectedCompletionDate = () => {
            if (task.expectedCompletionDate) {
              const expectedCompletionDateObj = moment.utc(task.expectedCompletionDate);
              const expectedCompletionDateObjAdd = expectedCompletionDateObj.add(1, 'year');
              expectedCompletionDate = expectedCompletionDateObjAdd.format('YYYY-MM-DD');
            } else {
              expectedCompletionDate = task.expectedCompletionDate;
            }
          };
          const oldTaskName = task.name.split('-');
          if (task.frequency === 'monthly') {
            taskName = oldTaskName.slice(0, -3).join('-');

            const oldTaskDate = oldTaskName[oldTaskName.length - 1];
            const oldYear = String(Number(oldTaskDate.slice(-4)) + 1);
            let oldString = oldTaskName[oldTaskName.length - 1].slice(-4);
            let splitString = oldTaskDate.split(oldString);
            let newDate = splitString.join(oldYear);
            oldTaskName[oldTaskName.length - 1] = newDate;

            nameExt = '- ' + newFinancialYear + ' -' + newDate;

            getStartDate();
            getDueDate();
            getExpectedCompletionDate();
          } else if (task.frequency === 'quarterly') {
            const oldName = task.name.split('-');
            taskName = oldName.slice(0, -4).join('-');
            let newString = String(Number(oldName[oldName.length - 1].slice(-4)) + 1);
            let newDateName = oldName[oldName.length - 1];
            let oldString = oldName[oldName.length - 1].slice(-4);
            let splitString = newDateName.split(oldString);
            let newDate = splitString.join(newString);
            oldName[oldName.length - 1] = newDate;
            let middleName = oldName[oldName.length - 2];
            nameExt = '- ' + newFinancialYear + ' -' + middleName + '-' + newDate;

            getStartDate();
            getDueDate();
            getExpectedCompletionDate();
          } else if (task.frequency === 'half_yearly') {
            const oldName = task.name.split('-');
            taskName = oldName.slice(0, -4).join('-');
            let newString = String(Number(oldName[oldName.length - 1].slice(-4)) + 1);
            let newDateName = oldName[oldName.length - 1];
            let oldString = oldName[oldName.length - 1].slice(-4);
            let splitString = newDateName.split(oldString);
            let newDate = splitString.join(newString);
            oldName[oldName.length - 1] = newDate;
            let middleName = oldName[oldName.length - 2];
            nameExt = '- ' + newFinancialYear + ' -' + middleName + '-' + newDate;

            getStartDate();
            getDueDate();
            getExpectedCompletionDate();
          } else if (task.frequency === 'yearly') {
            const oldName = task.name.split('-');
            taskName = oldName.slice(0, -3).join('-');
            const newDate = oldName[oldName.length - 1];
            nameExt = '- ' + newFinancialYear + ' -' + newDate;

            getStartDate();
            getDueDate();
            getExpectedCompletionDate();
          } else {
            taskName = task.name;

            getStartDate();
            getDueDate();
            getExpectedCompletionDate();
          }

          let PendingTask = new Task();
          PendingTask.name = taskName + nameExt;
          PendingTask.category = task.category;
          PendingTask.subCategory = task.subCategory;
          PendingTask.client = task.client;
          PendingTask.clientGroup = task.clientGroup;
          PendingTask.priority = task.priority;
          PendingTask.feeType = task.feeType;
          PendingTask.feeAmount = task.feeAmount;
          PendingTask.budgetedhours = task.budgetedhours;
          PendingTask.labels = task.labels;
          PendingTask.members = task.members;
          PendingTask.taskLeader = task.taskLeader;
          PendingTask.organization = user.organization;
          PendingTask.financialYear = newFinancialYear;
          PendingTask.recurring = true;
          PendingTask.recurringProfile = recurringProfile;
          PendingTask.frequency = task.frequency;
          PendingTask.taskStartDate = StartDate;
          PendingTask.dueDate = dueDate;
          PendingTask.expectedCompletionDate = expectedCompletionDate;
          PendingTask.recurringStatus = TaskRecurringStatus.PENDING;
          PendingTask.restore = TaskStatusEnum.PENDING;
          PendingTask.budgetedhours = task.budgetedhours;
          PendingTask.taskLeader = task.taskLeader;
          PendingTask.isUdin = task.isUdin;

          if (service) {
            PendingTask.name = service.name + nameExt;
            PendingTask.description = service.description;
            PendingTask.category = service.category;
            PendingTask.subCategory = service.subCategory;
            PendingTask.service = service;
          }
          if (appHier) {
            const approval = await ApprovalProcedures.findOne({
              where: { id: appHier, organization: user.organization.id },
              relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
              select: ['id'],
            });
            PendingTask.approvalProcedures = approval;
          }
          PendingTask['userId'] = user.id;
          await Task.save(PendingTask);
          if (data?.isUdin) {
            let udintask = new UdinTask();
            udintask.task = PendingTask;
            udintask.udinTaskStatus = UdinTaskStatus.ACTIVE;
            udintask.userType = userType.ORGANIZATION;
            udintask.organization = user.organization;
            udintask.recurringProfile = recurringProfile;
            await udintask.save();
          }

          for (let item of data?.userBudgetedHours) {
            let userData = await User.findOne({
              where: { id: item.id },
              relations: ['organization'],
            });

            let budgetedHours = new BudgetedHours();
            budgetedHours.status = BudgetedHourStatus.ACTIVE;
            budgetedHours.budgetedHours = item['userBudgetedHoursInSeconds'];
            budgetedHours.organization = user.organization;
            budgetedHours.task = PendingTask;
            budgetedHours.user = userData;
            await budgetedHours.save();
          }
        }
      }
    };
    for (const task of tasks) {
      if (task.recurringStatus === TaskRecurringStatus.CREATED) {
        await taskRecurring(task);
      }
    }
    let activity = new Activity();
    activity.action = Event_Actions.RECURRING_PROFILE_CREATED;
    activity.actorId = user.id;
    activity.type = client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
    activity.typeId = client ? client?.id : clientGroup?.id;
    activity.remarks = `"${service?.name || data.name}" Recurring Profile Created by ${
      user.fullName
    }`;
    await activity.save();
  }
  async findpending(userId: number, query: FindTasksQuery) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;
    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.status',
        'task.recurring',
        'task.taskStartDate',
        'task.financialYear',
        'task.frequency',
        'task.recurringStatus',
        'client.displayName',
        'client.id',
        'client.clientId',
        'client.status',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroup.status',
        'members.fullName',
        'taskMembers.fullName',
        'taskMembers.image',
        'taskLeader.fullName',
        'taskLeader.image',
        'leaderImageStorage.file',
        'taskStatus.id',
        'subtasks.name',
        'recurringProfile',
        'imageStorage.file',
      ])

      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.subTasks', 'subtasks')
      .leftJoin('task.members', 'members')
      .leftJoin('task.taskStatus', 'taskStatus')
      .leftJoin('task.members', 'taskMembers')
      .leftJoin('taskMembers.imageStorage', 'imageStorage')
      .leftJoin('task.taskLeader', 'taskLeader')
      .leftJoin('task.parentTask', 'parentTask')
      .leftJoin('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoin('task.recurringProfile', 'recurringProfile')
      .where('task.organization.id = :organization', { organization: user.organization.id })
      .andWhere('(task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.PENDING,
      })
      .andWhere(
        `task.status NOT IN ('${TaskStatusEnum.COMPLETED}', '${TaskStatusEnum.DELETED}', '${TaskStatusEnum.TERMINATED}')`,
      )
      .andWhere(
        new Brackets((qb) => {
          qb.where('client.id IS NULL OR client.status <> :clientStatus', {
            clientStatus: UserStatus.DELETED,
          }).andWhere('clientGroup.id IS NULL OR clientGroup.status <> :clientGroupStatus', {
            clientGroupStatus: UserStatus.DELETED,
          });
        }),
      );

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        name: 'task.name',
        frequency: 'task.frequency',
        priority: 'task.priority',
        task_start_date: 'task.taskStartDate',
        due_date: 'task.dueDate',
        clientNumber: 'client.clientNumber',
        displayName: 'client.displayName',
        category: 'client.category',
        active: 'client.active',
      };

      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.taskStartDate', 'ASC');
    }
    if (assigned) {
      tasks.andWhere('taskMembers.id = :userId', { userId });
    }

    if (query['recurringType'] === 'recurring') {
      tasks.andWhere('task.recurring is true');
    } else if (query['recurringType'] === 'non-recurring') {
      tasks.andWhere('task.recurring is false');
    }

    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask IS NULL')
        : query.allTasks === 'sub_task'
        ? tasks.andWhere('task.parentTask IS NOT NULL')
        : '';
    }

    if (query.client) {
      tasks.andWhere('task.client.id = :client', {
        client: query.client,
      });
    }

    if (query.clientGroup) {
      tasks.andWhere('task.clientGroup.id = :clientGroup', {
        clientGroup: query.clientGroup,
      });
    }

    if (query.category) {
      tasks.andWhere('category.id in (:...categories)', {
        categories: query.category,
      });
    }

    if (query.subCategory) {
      tasks.andWhere('subCategory.id in (:...subCategories)', {
        subCategories: query.subCategory,
      });
    }

    if (query.clientCategory) {
      tasks.andWhere('client.category in (:...clientCategories)', {
        clientCategories: query.clientCategory,
      });
    }

    if (query.clientSubCategory) {
      tasks.andWhere('client.subCategory in (:...clientSubCategories)', {
        clientSubCategories: query.clientSubCategory,
      });
    }

    if (query.assignee) {
      tasks.andWhere('members.id in (:...assignee)', {
        assignee: query.assignee,
      });
    }
    if (query?.billable?.length) {
      let billableType = [];
      if (query?.billable?.includes('billable')) {
        billableType.push(true);
      }
      if (query?.billable?.includes('nonbillable')) {
        billableType.push(false);
      }
      tasks.andWhere('task.billable in (:...billable)', {
        billable: billableType,
      });
    }
    if (query.taskLeader) {
      tasks.andWhere('taskLeader.id in (:...taskLeader)', {
        taskLeader: query.taskLeader,
      });
    }

    if (query.createdBy) {
      tasks.andWhere('user.id in (:...createdBy)', {
        createdBy: query.createdBy,
      });
    }

    if (query.status) {
      tasks.andWhere('task.status in (:...status)', {
        status: query.status,
      });
    }

    if (query.priority) {
      tasks.andWhere('task.priority in (:...priority)', {
        priority: query.priority,
      });
    }

    if (query.financialYear) {
      tasks.andWhere('task.financialYear in (:...financialYear)', {
        financialYear: query.financialYear,
      });
    }

    if (query.tags) {
      tasks.andWhere('labels.id in (:...labels)', {
        labels: query.tags,
      });
    }

    if (query.completedOn) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` Date((select
          max(created_at) from task_status ts
          where ts.task_id = task.id
          and ts.status = "completed"
          order by ts.created_at desc
          limit 1)) = :completedOn`,
        { completedOn: '2022-06-10' },
      );
    }

    if (query.completedBy) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` (select
          user_id from task_status ts
          where ts.task_id = task.id
          and ts.status = "completed"
          order by ts.created_at desc
          limit 1) in (:...completedBy)`,
        { completedBy: query.completedBy },
      );
    }

    if (query.search) {
      tasks.andWhere(
        `(
          task.name like :search or
          client.displayName like :search or 
          clientGroup.displayName like :search
        )`,
        { search: '%' + query.search + '%' },
      );
    }

    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (query.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    if (query?.offset >= 0) {
      tasks.skip(query?.offset);
    }

    if (query?.limit) {
      tasks.take(query?.limit);
    }

    let result = await tasks.getManyAndCount();

    return result;
  }

  async getPedningTasks(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const getFilteredIds = query.selected.map((item) => Number(item));
    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoin('task.taskStatus', 'taskStatus')
      .where('task.organization.id = :organization', { organization: user.organization.id })
      .andWhere('(task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.PENDING,
      })
      .andWhere(
        `task.status NOT IN ('${TaskStatusEnum.COMPLETED}', '${TaskStatusEnum.DELETED}', '${TaskStatusEnum.TERMINATED}')`,
      )
      .andWhere('client.id IN (:...clientIds)', { clientIds: getFilteredIds })
      .groupBy('client.id');
    const data = await tasks.getMany();
    return data;
  }

  async getGroupPedningTasks(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const getFilteredIds = query.selected.map((item) => Number(item));
    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoin('task.taskStatus', 'taskStatus')
      .where('task.organization.id = :organization', { organization: user.organization.id })
      .andWhere('(task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.PENDING,
      })
      .andWhere(
        `task.status NOT IN ('${TaskStatusEnum.COMPLETED}', '${TaskStatusEnum.DELETED}', '${TaskStatusEnum.TERMINATED}')`,
      )
      .andWhere('clientGroup.id IN (:...clientGroupIds)', { clientGroupIds: getFilteredIds })
      .groupBy('clientGroup.id');
    const data = await tasks.getMany();
    return data;
  }

  async getUserUpcomingTasks(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoin('task.taskStatus', 'taskStatus')
      .leftJoinAndSelect('task.parentTask', 'parentTask')
      .where('task.organization.id = :organization', { organization: user.organization.id })
      .andWhere('(task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.PENDING,
      })
      .andWhere(
        `task.status NOT IN ('${TaskStatusEnum.COMPLETED}', '${TaskStatusEnum.DELETED}', '${TaskStatusEnum.TERMINATED}')`,
      );

    if (query?.userId) {
      tasks.andWhere('members.id = :userId', { userId: parseInt(query.userId) });
    }
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        displayName: 'client.displayName',
        priority: 'task.priority',
        name: 'task.name',
        task_start_date: 'task.taskStartDate',
        due_date: 'task.dueDate',
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.taskStartDate', 'ASC');
    }
    if (query.taskValue) {
      query.taskValue === 'main_task'
        ? tasks.andWhere('task.parentTask IS NULL')
        : query.taskValue === 'sub_task'
        ? tasks.andWhere('task.parentTask IS NOT NULL')
        : '';
    }

    if (query?.recuuringType === 'recurring') {
      tasks.andWhere('task.recurring is true');
    } else if (query?.recuuringType === 'non-recurring') {
      tasks.andWhere('task.recurring is false');
    }

    // if (query?.search) {
    //   const search = query.search.trim();
    //   if (search) {
    //     tasks.andWhere('task.name LIKE :search', { search: `%${search}%` });
    //   }
    // }

    if (query?.search) {
      const search = query.search.trim();
      if (search) {
        tasks.andWhere(
          new Brackets((qb) => {
            qb.where('task.name LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('clientGroup.displayName LIKE :search', {
              search: `%${query.search}%`,
            });
          }),
        );
      }
    }
    if (query?.fromStartDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', { fromDate: query?.fromStartDate });
    }

    if (query?.toStartDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: query?.toStartDate,
      });
    }

    if (query?.fromDueDate) {
      tasks.andWhere('task.dueDate >= :fromDueDate', { fromDueDate: query?.fromDueDate });
    }

    if (query?.toDueDate) {
      tasks.andWhere('task.dueDate <= :toDueDate', {
        toDueDate: query?.toDueDate,
      });
    }

    if (query?.fromExpectedDate) {
      tasks.andWhere('task.expectedCompletionDate >= :fromExpectedDate', {
        fromExpectedDate: query?.fromExpectedDate,
      });
    }

    if (query?.toExpectedDate) {
      tasks.andWhere('task.expectedCompletionDate <= :toExpectedDate', {
        toExpectedDate: query?.toExpectedDate,
      });
    }

    if (query?.priority) {
      tasks.andWhere('task.priority = :priority', { priority: query?.priority });
    }

    if (query?.offset >= 0) {
      tasks.skip(query?.offset);
    }

    if (query?.limit) {
      tasks.take(query?.limit);
    }
    const data = await tasks.getManyAndCount();

    return data;
  }

  async exportUserUpcomingAssignedTasks(userId: number, query: FindUserTasksDto) {
    let tasks: any = await this.getUserUpcomingTasks(userId, query);

    if (!tasks.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Upcoming Task (Assignee)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Priority', key: 'priority' },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks[0].forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        taskName: task?.name,
        priority: task?.priority,
        taskStatus: getTitle(task?.status),
        startDate: formatDate(task?.taskStartDate),
        dueDate: formatDate(task?.dueDate),
      };

      const row = worksheet.addRow(rowData);
      const priorityCell = row.getCell('priority');
      switch (rowData.priority?.toLowerCase()) {
        case 'high':
          priorityCell.font = { color: { argb: 'FB0505' }, bold: true };
          break;
        case 'medium':
          priorityCell.font = { color: { argb: 'F17F23' }, bold: true };
          break;
        case 'low':
          priorityCell.font = { color: { argb: '019335' }, bold: true };
          break;
        default:
          priorityCell.font = { color: { argb: '64B5F6' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportuserUpcomingAssaignedTasks(userId: number, query: FindUserTasksDto) {
    let tasks: any = await this.getUserUpcomingTasks(userId, query);

    if (!tasks.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Upcoming Task (Assignee)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Priority', key: 'priority' },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Task Leader', key: 'taskLeader' },
      { header: 'Members', key: 'members' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks[0].forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        taskName: task?.name,
        priority: getTitle(task?.priority),
        taskStatus: getTitle(task?.status),
        startDate: formatDate(task?.taskStartDate),
        dueDate: formatDate(task?.dueDate),
        taskLeader: task?.taskLeader?.fullName,
        members: task?.members?.map((member) => member?.fullName).join(', '),
      };

      const row = worksheet.addRow(rowData);
      const priorityCell = row.getCell('priority');
      switch (rowData.priority?.toLowerCase()) {
        case 'high':
          priorityCell.font = { color: { argb: 'FB0505' }, bold: true };
          break;
        case 'medium':
          priorityCell.font = { color: { argb: 'F17F23' }, bold: true };
          break;
        case 'low':
          priorityCell.font = { color: { argb: '019335' }, bold: true };
          break;
        default:
          priorityCell.font = { color: { argb: '64B5F6' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async find(userId: number, query: FindTasksQuery) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });
    const completedDays = organizationPreferences?.taskPreferences?.['taskDate'] || 15;

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;
    let tasks = createQueryBuilder(Task, 'task').select([
      'task.id',
      'task.taskNumber',
      'task.name',
      'task.dueDate',
      'task.financialYear',
      'task.priority',
      'task.status',
      'task.recurringStatus',
      'task.createdAt',
      'task.approvalStatus',
      'task.recurring',
      'task.billable',
      'task.createdDate',
      'task.paymentStatus',
      'task.feeAmount',
      'client.id',
      'client.displayName',
      'clientGroup.id',
      'clientGroup.displayName',
      // 'members.id',
      // 'members.fullName',
      // 'taskLeader.id',
      // 'taskLeader.fullName',
      'user.id',
      // 'approvalProcedures.id'
    ]);
    // .leftJoin('task.members', 'members')
    // .leftJoin('task.taskLeader', 'taskLeader')
    if (query.category) {
      tasks.leftJoin('task.category', 'category');
    }
    if (query.subCategory) {
      tasks.leftJoin('task.subCategory', 'subCategory');
    }
    tasks
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.user', 'user');

    tasks
      // .leftJoinAndSelect('task.expenditure', 'expenditure')
      // .leftJoin('task.approvalProcedures', 'approvalProcedures')
      // .leftJoin('approvalProcedures.approval', 'approval')
      // .leftJoin('approval.approvalLevels', 'approvalLevels')
      // .leftJoin('approvalLevels.user', 'approvalLevelsUsers')
      .leftJoin('task.service', 'service')
      .where('task.organization.id = :organization', { organization: user.organization.id });

    if (!query.subTasks) {
      tasks.andWhere('task.parentTask IS NULL');
    }
    if (query?.mainTaks && query.mainTaks) {
      tasks.andWhere('task.parentTask IS NOT NULL');
    }

    if (assigned) {
      const subQuery = tasks
        .subQuery()
        .select('taskMembers.task_id')
        .from('task_members_user', 'taskMembers')
        .where('taskMembers.user_id = :userId')
        .getQuery();

      const subQuery2 = tasks
        .subQuery()
        .select('taskTeaders.task_id')
        .from('task_task_leader_user', 'taskTeaders')
        .where('taskTeaders.user_id=:userId')
        .getQuery();

      tasks.andWhere(
        new Brackets((qb) => {
          // qb.where('approvalLevelsUsers.id = :userId AND task.status = :statusApproval', {
          //   userId,
          //   statusApproval: TaskStatusEnum.UNDER_REVIEW,
          // })
          qb.where(`task.id IN ${subQuery}`, { userId }).orWhere(`task.id IN ${subQuery2}`, {
            userId,
          });
          // .orWhere('taskLeader.id = :userId', { userId });
        }),
      );
    }

    tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    });

    tasks.andWhere(
      new Brackets((qb) =>
        qb
          .where('task.status IN (:...statuses)', {
            statuses: [
              TaskStatusEnum.TODO,
              TaskStatusEnum.IN_PROGRESS,
              TaskStatusEnum.ON_HOLD,
              TaskStatusEnum.UNDER_REVIEW,
            ],
          })
          .orWhere('task.status = :completedStatus', {
            completedStatus: TaskStatusEnum.COMPLETED,
          })
          .andWhere('task.statusUpdatedAt >= :todayDate', {
            todayDate: moment().subtract(parseInt(completedDays), 'days').toDate(),
          }),
      ),
    );

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        dueDate: 'task.dueDate',
        priority: 'task.priority',
        status: 'task.status',
        financialYear: 'task.financialYear',
      };

      const column = columnMap[sort.column] || sort.column;
      tasks.addOrderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.addOrderBy('task.createdDate', 'DESC').addOrderBy('task.id', 'DESC');
    }

    if (query.completedOn && !query.createdOn) {
      tasks.andWhere(`task.status=:status`, { status: TaskStatusEnum.COMPLETED });
    }

    if (query.fy) {
      tasks.andWhere('task.financialYear in (:...fy)', {
        fy: query.fy,
      });
    }

    if (query.client) {
      tasks.andWhere('task.client.id = :client', {
        client: query.client,
      });
    }

    if (query.clientGroup) {
      tasks.andWhere('task.clientGroup.id = :clientGroup', {
        clientGroup: query.clientGroup,
      });
    }
    if (query.category) {
      tasks.andWhere('category.id in (:...categories)', {
        categories: query.category,
      });
    }

    if (query.subCategory) {
      tasks.andWhere('subCategory.id in (:...subCategories)', {
        subCategories: query.subCategory,
      });
    }

    if (query.assignee) {
      const subQuery = tasks
        .subQuery()
        .select('taskMembers.task_id')
        .from('task_members_user', 'taskMembers')
        .where('taskMembers.user_id IN (:...assignee)', { assignee: query.assignee })
        .getQuery();

      tasks.andWhere(`task.id IN ${subQuery}`);
    }

    if (query.taskLeader) {
      const subQuery = tasks
        .subQuery()
        .select('taskTeaders.task_id')
        .from('task_task_leader_user', 'taskTeaders')
        .where('taskTeaders.user_id IN (:...taskLeader)', { taskLeader: query.taskLeader })
        .getQuery();
      tasks.andWhere(`task.id IN ${subQuery}`);

      // tasks.andWhere('taskLeader.id in (:...taskLeader)', {
      //   taskLeader: query.taskLeader,
      // });
    }

    if (query?.createdBy?.includes('Automatically')) {
      tasks.andWhere('task.recurring is true');
    }

    if (query.createdBy) {
      if (query.createdBy.includes('Automatically')) {
        query.createdBy = query.createdBy.filter((item) => item !== 'Automatically');
        if (query.createdBy.length) {
          tasks.andWhere('user.id in (:...createdBy)', {
            createdBy: query.createdBy,
          });
        }
      } else {
        tasks.andWhere('user.id in (:...createdBy)', {
          createdBy: query.createdBy,
        });
      }
    }

    const dueStatuses: string[] =
      query.status
        ?.filter((status) => status.endsWith('_overdue'))
        .map((status) => status.replace('_overdue', '')) || [];
    const nonDueStatuses: string[] =
      query.status?.filter((status) => !status.endsWith('_overdue')) || [];
    const hasCompletedOverdue = query.status?.includes('completed_overdue') || false;

    tasks.andWhere(
      new Brackets((qb) => {
        const today = moment().format('YYYY-MM-DD');
        let hasConditions = false;

        // Handle non-overdue statuses
        if (nonDueStatuses.length) {
          const includesCompleted = nonDueStatuses.includes(TaskStatusEnum.COMPLETED);
          const filteredNonDueStatuses = includesCompleted
            ? nonDueStatuses.filter((status) => status !== TaskStatusEnum.COMPLETED)
            : nonDueStatuses;

          if (filteredNonDueStatuses.length) {
            qb.where('task.status IN (:...nonDueStatuses) AND task.dueDate >= :today', {
              nonDueStatuses: filteredNonDueStatuses,
              today,
            });
            hasConditions = true;
          }

          if (includesCompleted) {
            const method = hasConditions ? 'orWhere' : 'where';
            qb[method](
              'task.status = :completedStatus AND Date(task.dueDate) >= Date(task.statusUpdatedAt)',
              {
                completedStatus: TaskStatusEnum.COMPLETED,
              },
            );
            hasConditions = true;
          }
        }

        // Handle overdue statuses
        if (dueStatuses.length || hasCompletedOverdue) {
          const includesCompleted = dueStatuses.includes(TaskStatusEnum.COMPLETED);
          const filteredDueStatuses = includesCompleted
            ? dueStatuses.filter((status) => status !== TaskStatusEnum.COMPLETED)
            : dueStatuses;
          if (filteredDueStatuses.length) {
            const method = hasConditions ? 'orWhere' : 'where';
            qb[method]('task.status IN (:...dueStatuses) AND task.dueDate < :today', {
              dueStatuses: filteredDueStatuses,
              today,
            });
            hasConditions = true;
          }

          if (hasCompletedOverdue) {
            const method = hasConditions ? 'orWhere' : 'where';
            qb[method]('task.status = :completedOverdue AND task.dueDate < :today', {
              completedOverdue: 'completed',
              today,
            });
            hasConditions = true;
          } else if (includesCompleted) {
            const method = hasConditions ? 'orWhere' : 'where';
            qb[method]('task.status = :completedStatus AND task.dueDate < task.statusUpdatedAt', {
              completedStatus: TaskStatusEnum.COMPLETED,
            });
          }
        }
      }),
    );

    if (query?.serviceDetails) {
      tasks.andWhere('service.id = :serviceId', {
        serviceId: query?.serviceDetails,
      });
    }

    if (query?.billingType?.length) {
      tasks.andWhere('task.paymentStatus in (:...paymentStatus)', {
        paymentStatus: query.billingType,
      });
    }

    if (query?.billable?.length) {
      let billableType = [];
      if (query?.billable?.includes('billable')) {
        billableType.push(true);
      }
      if (query?.billable?.includes('nonbillable')) {
        billableType.push(false);
      }
      tasks.andWhere('task.billable in (:...billable)', {
        billable: billableType,
      });
    }

    if (query.priority) {
      tasks.andWhere('task.priority in (:...priority)', {
        priority: query.priority,
      });
    }

    if (query.financialYear) {
      tasks.andWhere('task.financialYear in (:...financialYear)', {
        financialYear: query.financialYear,
      });
    }

    if (query.tags) {
      tasks.andWhere('labels.id in (:...labels)', {
        labels: query.tags,
      });
    }

    if (query.completedBy) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` (select
          user_id from task_status ts
          where ts.task_id = task.id
          and ts.status = "completed"
          order by ts.created_at desc
          limit 1) in (:...completedBy)`,
        { completedBy: query.completedBy },
      );
    }

    if (query.search) {
      tasks.andWhere(
        `(
          task.name like :search or
          task.taskNumber like :search or
          client.displayName like :search or
          clientGroup.displayName like :search
        )`,
        { search: '%' + query.search + '%' },
      );
    }

    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (query.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    if (query?.removeCompleted) {
      tasks.andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      });
    }

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'expectedCompletionDate',
      dateFilterKey: DateFilterKeys.EXPECTED_COMPLETION_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'statusUpdatedAt',
      dateFilterKey: DateFilterKeys.COMPLETED_ON,
    });

    if (query.offset) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }
    const [t, c] = await Promise.all([tasks.getMany(), tasks.getCount()]);

    if (t.length === 0) {
      return { t: [], c };
    }

    const taskIds = t.map((task) => task.id);
    const members = await Task.createQueryBuilder('task')
      .select(['task.id', 'members.id', 'members.fullName'])
      .leftJoin('task.members', 'members')
      .where('task.id IN (:...taskIds)', { taskIds })
      .getMany();
    // Create a map for quick lookup
    const memberMap = new Map(members.map((task) => [task.id, task.members]));

    // Assign only required member fields to each task
    const tasksWithMembers = t.map((task) => ({
      ...task,
      members: memberMap.get(task.id) || [],
    }));

    const leaders = await Task.createQueryBuilder('task')
      .select(['task.id', 'leader.id', 'leader.fullName'])
      .leftJoin('task.taskLeader', 'leader')
      .where('task.id IN (:...taskIds)', { taskIds })
      .getMany();
    // Create a map for quick lookup
    const leaderMap = new Map(leaders.map((task) => [task.id, task.taskLeader]));

    const tasksWithLeaders = tasksWithMembers.map((task) => ({
      ...task,
      taskLeader: leaderMap.get(task.id) || [],
    }));

    const expenditure = await Task.createQueryBuilder('task')
      .leftJoinAndSelect('task.expenditure', 'expenditure')
      .select(['task.id', 'expenditure'])
      .where('task.id IN (:...taskIds)', { taskIds })
      .getMany();

    const expenditureMap = new Map(expenditure.map((e) => [e.id, e.expenditure]));
    const tasksWithExpenditures = tasksWithLeaders.map((task) => ({
      ...task,
      expenditure: expenditureMap.get(task.id) || [],
    }));

    // console.log({ tasksWithMembers });

    return [tasksWithExpenditures, c];
  }

  // async find(userId: number, query: FindTasksQuery) {
  //   let user = await User.findOne({
  //     where: { id: userId },
  //     relations: ['organization', 'role', 'role.permissions'],
  //   });

  //   let allTasks = user.role.permissions.find(
  //     (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
  //   );

  //   let assignedTasks = user.role.permissions.find(
  //     (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
  //   );

  //   const organizationPreferences = await OrganizationPreferences.findOne({
  //     where: { organization: user.organization.id },
  //     order: { id: 'DESC' },
  //   });
  //   const completedDays = organizationPreferences?.taskPreferences?.['taskDate'] || 15;

  //   let all = allTasks ? true : false;
  //   let assigned = !all && assignedTasks ? true : false;
  //   let tasks = createQueryBuilder(Task, 'task')
  //     .select([
  //       'task.id',
  //       'task.taskNumber',
  //       'task.name',
  //       'task.dueDate',
  //       'task.priority',
  //       'task.status',
  //       'task.recurringStatus',
  //       'task.createdAt',
  //       'task.approvalStatus',
  //       'task.recurring',
  //       'task.billable',
  //       'task.createdDate',
  //       'task.paymentStatus',
  //       'task.feeAmount',
  //       'client.id',
  //       'client.displayName',
  //       'clientGroup.id',
  //       'clientGroup.displayName',
  //       'members.id',
  //       'members.fullName',
  //       'taskLeader.id',
  //       'taskLeader.fullName',
  //       'user.id',
  //     ])
  //     .leftJoin('task.members', 'members')
  //     .leftJoin('task.taskLeader', 'taskLeader')
  //     .leftJoin('task.client', 'client')
  //     .leftJoin('task.clientGroup', 'clientGroup')
  //     .leftJoin('task.user', 'user');

  //   tasks
  //     .leftJoinAndSelect('task.expenditure', 'expenditure')

  //     .leftJoin('task.service', 'service')
  //     .where('task.organization.id = :organization', { organization: user.organization.id });

  //   if (!query.subTasks) {
  //     tasks.andWhere('task.parentTask IS NULL');
  //   }
  //   if (query?.mainTaks && query.mainTaks) {
  //     tasks.andWhere('task.parentTask IS NOT NULL');
  //   }

  //   if (assigned) {
  //     const subQuery = tasks
  //       .subQuery()
  //       .select('taskMembers.task_id')
  //       .from('task_members_user', 'taskMembers')
  //       .where('taskMembers.user_id = :userId')
  //       .getQuery();

  //     tasks.andWhere(
  //       new Brackets((qb) => {
  //         qb.where(`task.id IN ${subQuery}`, { userId })
  //         qb.where('taskLeader.id = :userId', { userId });
  //       }),
  //     );
  //   }

  //   tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
  //     recurringStatus: TaskRecurringStatus.CREATED,
  //   });

  //   tasks
  //     .andWhere(
  //       new Brackets((qb) =>
  //         qb
  //           .where('task.status IN (:...statuses)', {
  //             statuses: [
  //               TaskStatusEnum.TODO,
  //               TaskStatusEnum.IN_PROGRESS,
  //               TaskStatusEnum.ON_HOLD,
  //               TaskStatusEnum.UNDER_REVIEW,
  //             ],
  //           })
  //           .orWhere('task.status = :completedStatus', {
  //             completedStatus: TaskStatusEnum.COMPLETED,
  //           })
  //           .andWhere('task.statusUpdatedAt >= :todayDate', {
  //             todayDate: moment().subtract(parseInt(completedDays), 'days').toDate(),
  //           }),
  //       ),
  //     )

  //   const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
  //   if (sort?.column) {
  //     const columnMap: Record<string, string> = {

  //       taskNumber: 'task.taskNumber',
  //       name: 'task.name',
  //       dueDate: 'task.dueDate',
  //       priority: 'task.priority',
  //       status: 'task.status',

  //     };

  //     const column = columnMap[sort.column] || sort.column;
  //     console.log(column, sort.direction.toUpperCase());
  //     tasks
  //       .addOrderBy(column, sort.direction.toUpperCase());
  //   } else {
  //     tasks
  //       .addOrderBy('task.createdDate', 'DESC')
  //       .addOrderBy('task.id', 'DESC');
  //   }

  //   if (query.completedOn && !query.createdOn) {
  //     tasks.andWhere(`task.status=:status`, { status: TaskStatusEnum.COMPLETED });
  //   }

  //   if (query.client) {
  //     tasks.andWhere('task.client.id = :client', {
  //       client: query.client,
  //     });
  //   }

  //   if (query.clientGroup) {
  //     tasks.andWhere('task.clientGroup.id = :clientGroup', {
  //       clientGroup: query.clientGroup,
  //     });
  //   }

  //   if (query.assignee) {
  //     const subQuery = tasks
  //       .subQuery()
  //       .select('taskMembers.task_id')
  //       .from('task_members_user', 'taskMembers')
  //       .where('taskMembers.user_id IN (:...assignee)', { assignee: query.assignee })
  //       .getQuery();

  //     tasks.andWhere(`task.id IN ${subQuery}`);
  //   }

  //   if (query.taskLeader) {
  //     tasks.andWhere('taskLeader.id in (:...taskLeader)', {
  //       taskLeader: query.taskLeader,
  //     });
  //   }

  //   if (query?.createdBy?.includes('Automatically')) {
  //     tasks.andWhere('task.recurring is true');
  //   }

  //   if (query.createdBy) {
  //     if (query.createdBy.includes('Automatically')) {
  //       query.createdBy = query.createdBy.filter((item) => item !== 'Automatically');
  //       if (query.createdBy.length) {
  //         tasks.andWhere('user.id in (:...createdBy)', {
  //           createdBy: query.createdBy,
  //         });
  //       }
  //     } else {
  //       tasks.andWhere('user.id in (:...createdBy)', {
  //         createdBy: query.createdBy,
  //       });
  //     }
  //   }

  //   let dueStatuses: any[] = query.status?.reduce((acc, status) => {
  //     if (status.endsWith('_overdue')) {
  //       acc.push(status.replace('_overdue', ''));
  //     }
  //     return acc;
  //   }, []);

  //   let nonDueStatuses: any[] = query.status?.filter((status) => !status.endsWith('_overdue'));
  //   tasks.andWhere(
  //     new Brackets((qb) => {
  //       let hasAddedCondition = false;
  //       let complitedStatus = false;
  //       if (nonDueStatuses?.length) {
  //         if (nonDueStatuses.includes(TaskStatusEnum.COMPLETED)) {
  //           nonDueStatuses = nonDueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
  //           complitedStatus = true;
  //         }
  //         if (nonDueStatuses?.length) {
  //           qb.where('task.status IN (:...nonDueStatuses) AND task.dueDate >= :todayDate', {
  //             nonDueStatuses: nonDueStatuses,
  //             todayDate: moment().format('YYYY-MM-DD').toString(),
  //           });
  //         }

  //         if (complitedStatus) {
  //           qb.andWhere('task.status=:comStatus AND Date(task.dueDate) >= Date(task.statusUpdatedAt)', {
  //             comStatus: TaskStatusEnum.COMPLETED,
  //           });
  //         }
  //         hasAddedCondition = true;
  //       }

  //       let dueComplited = false;
  //       if (dueStatuses?.length) {
  //         if (dueStatuses.includes(TaskStatusEnum.COMPLETED)) {
  //           dueStatuses = dueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
  //           dueComplited = true;
  //         }

  //         if (hasAddedCondition) {
  //           if (dueStatuses?.length) {
  //             qb.orWhere('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
  //               dueStatuses: dueStatuses,
  //               todayDatee: moment().format('YYYY-MM-DD').toString(),
  //             });
  //           }

  //           if (dueComplited) {
  //             qb.orWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
  //               dueComplited: TaskStatusEnum.COMPLETED,
  //             });
  //           }
  //         } else {
  //           if (dueStatuses?.length) {
  //             qb.where('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
  //               dueStatuses: dueStatuses,
  //               todayDatee: moment().format('YYYY-MM-DD').toString(),
  //             });
  //           }

  //           if (dueComplited) {
  //             qb.andWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
  //               dueComplited: TaskStatusEnum.COMPLETED,
  //             });
  //           }
  //         }
  //       }
  //     }),
  //   );

  //   if (query?.serviceDetails) {
  //     tasks.andWhere('service.id = :serviceId', {
  //       serviceId: query?.serviceDetails,
  //     });
  //   }

  //   if (query?.billingType?.length) {
  //     tasks.andWhere('task.paymentStatus in (:...paymentStatus)', {
  //       paymentStatus: query.billingType,
  //     });
  //   }

  //   if (query?.billable?.length) {
  //     let billableType = [];
  //     if (query?.billable?.includes('billable')) {
  //       billableType.push(true);
  //     }
  //     if (query?.billable?.includes('nonbillable')) {
  //       billableType.push(false);
  //     }
  //     tasks.andWhere('task.billable in (:...billable)', {
  //       billable: billableType,
  //     });
  //   }

  //   if (query.priority) {
  //     tasks.andWhere('task.priority in (:...priority)', {
  //       priority: query.priority,
  //     });
  //   }

  //   if (query.financialYear) {
  //     tasks.andWhere('task.financialYear in (:...financialYear)', {
  //       financialYear: query.financialYear,
  //     });
  //   }

  //   if (query.tags) {
  //     tasks.andWhere('labels.id in (:...labels)', {
  //       labels: query.tags,
  //     });
  //   }

  //   if (query.completedBy) {
  //     tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
  //     tasks.andWhere(
  //       ` (select
  //         user_id from task_status ts
  //         where ts.task_id = task.id
  //         and ts.status = "completed"
  //         order by ts.created_at desc
  //         limit 1) in (:...completedBy)`,
  //       { completedBy: query.completedBy },
  //     );
  //   }

  //   if (query.search) {
  //     tasks.andWhere(
  //       `(
  //         task.name like :search or
  //         task.taskNumber like :search or
  //         client.displayName like :search or
  //         clientGroup.displayName like :search
  //       )`,
  //       { search: '%' + query.search + '%' },
  //     );
  //   }

  //   if (query.taskType?.length < 2) {
  //     if (query.taskType?.includes('recurring')) {
  //       tasks.andWhere('task.recurring is true');
  //     }

  //     if (query.taskType?.includes('non_recurring')) {
  //       tasks.andWhere('task.recurring is false');
  //     }
  //   }

  //   if (query?.removeCompleted) {
  //     tasks.andWhere('task.status IN (:...statuses)', {
  //       statuses: [
  //         TaskStatusEnum.TODO,
  //         TaskStatusEnum.IN_PROGRESS,
  //         TaskStatusEnum.ON_HOLD,
  //         TaskStatusEnum.UNDER_REVIEW,
  //       ],
  //     });
  //   }

  //   this.filterByDate({
  //     query,
  //     tasks,
  //     entityKey: 'taskStartDate',
  //     dateFilterKey: DateFilterKeys.START_DATE,
  //   });

  //   this.filterByDate({
  //     query,
  //     tasks,
  //     entityKey: 'expectedCompletionDate',
  //     dateFilterKey: DateFilterKeys.EXPECTED_COMPLETION_DATE,
  //   });

  //   this.filterByDate({
  //     query,
  //     tasks,
  //     entityKey: 'dueDate',
  //     dateFilterKey: DateFilterKeys.DUE_ON,
  //   });

  //   this.filterByDate({
  //     query,
  //     tasks,
  //     entityKey: 'createdAt',
  //     dateFilterKey: DateFilterKeys.CREATED_ON,
  //   });

  //   this.filterByDate({
  //     query,
  //     tasks,
  //     entityKey: 'statusUpdatedAt',
  //     dateFilterKey: DateFilterKeys.COMPLETED_ON,
  //   });

  //   if (query.offset) {
  //     tasks.skip(query.offset);
  //   }

  //   if (query.limit) {
  //     tasks.take(query.limit);
  //   }

  //   // let [t, c] = await tasks.getManyAndCount();
  //   // const t = await tasks.getMany();
  //   // const c = await tasks.getCount();
  //   // console.log(tasks.getSql());
  //   const [t, c] = await Promise.all([
  //     tasks.getMany(),
  //     tasks.getCount(),
  //   ]);

  //   // const taskIds = t.map(task => task.id);

  //   // const members = await Task.createQueryBuilder('task')
  //   //   .select(['task.id', 'members.id', 'members.fullName'])
  //   //   .leftJoin('task.members', 'members')
  //   //   .where('task.id IN (:...taskIds)', { taskIds })
  //   //   .getMany();

  //   // // Create a map for quick lookup
  //   // const memberMap = new Map(
  //   //   members.map(task => [task.id, task.members])
  //   // );

  //   // // Assign only required member fields to each task
  //   // const tasksWithMembers = t.map(task => ({
  //   //   ...task,
  //   //   members: memberMap.get(task.id) || [],
  //   // }));

  //   // return { t: tasksWithMembers, c };
  //   return { t, c }

  // }

  async findApprovalTasks(userId: number, query: FindTasksQuery) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });
    const completedDays = organizationPreferences?.taskPreferences?.['taskDate'] || 15;

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;
    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.financialYear',
        'task.status',
        'task.recurringStatus',
        'task.createdAt',
        'task.approvalStatus',
        'task.recurring',
        'task.billable',
        'task.createdDate',
        'task.paymentStatus',
        'task.feeAmount',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName',
        // 'members.id',
        // 'members.fullName',
        // 'taskLeader.id',
        // 'taskLeader.fullName',
        'user.id',
        'approvalProcedures.id',
      ])
      // .leftJoin('task.members', 'members')
      // .leftJoin('task.taskLeader', 'taskLeader')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.user', 'user');

    tasks
      .leftJoinAndSelect('task.expenditure', 'expenditure')
      .leftJoin('task.approvalProcedures', 'approvalProcedures')
      .leftJoin('approvalProcedures.approval', 'approval')
      .leftJoin('approval.approvalLevels', 'approvalLevels')
      .leftJoin('approvalLevels.user', 'approvalLevelsUsers')
      .leftJoin('task.service', 'service')
      .where('task.organization.id = :organization', { organization: user.organization.id });

    if (!query.subTasks) {
      tasks.andWhere('task.parentTask IS NULL');
    }
    if (query?.mainTaks && query.mainTaks) {
      tasks.andWhere('task.parentTask IS NOT NULL');
    }

    // if (assigned) {
    const subQuery = tasks
      .subQuery()
      .select('taskMembers.task_id')
      .from('task_members_user', 'taskMembers')
      .where('taskMembers.user_id = :userId')
      .getQuery();

    tasks.andWhere(
      new Brackets((qb) => {
        qb.where('approvalLevelsUsers.id = :userId AND task.status = :statusApproval', {
          userId,
          statusApproval: TaskStatusEnum.UNDER_REVIEW,
        });
        // qb.where(`task.id IN ${subQuery}`, { userId })
        // qb.where('taskLeader.id = :userId', { userId });
      }),
    );
    // }

    tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    });

    tasks.andWhere(
      new Brackets((qb) =>
        qb
          .where('task.status IN (:...statuses)', {
            statuses: [
              TaskStatusEnum.TODO,
              TaskStatusEnum.IN_PROGRESS,
              TaskStatusEnum.ON_HOLD,
              TaskStatusEnum.UNDER_REVIEW,
            ],
          })
          .orWhere('task.status = :completedStatus', {
            completedStatus: TaskStatusEnum.COMPLETED,
          })
          .andWhere('task.statusUpdatedAt >= :todayDate', {
            todayDate: moment().subtract(parseInt(completedDays), 'days').toDate(),
          }),
      ),
    );

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        dueDate: 'task.dueDate',
        priority: 'task.priority',
        status: 'task.status',
        financialYear: 'task.financialYear',
      };

      const column = columnMap[sort.column] || sort.column;
      tasks.addOrderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.addOrderBy('task.createdDate', 'DESC').addOrderBy('task.id', 'DESC');
    }

    if (query.completedOn && !query.createdOn) {
      tasks.andWhere(`task.status=:status`, { status: TaskStatusEnum.COMPLETED });
    }

    if (query.client) {
      tasks.andWhere('task.client.id = :client', {
        client: query.client,
      });
    }

    if (query.clientGroup) {
      tasks.andWhere('task.clientGroup.id = :clientGroup', {
        clientGroup: query.clientGroup,
      });
    }

    if (query?.assignee?.length) {
      const subQuery = tasks
        .subQuery()
        .select('taskMembers.task_id')
        .from('task_members_user', 'taskMembers')
        .where('taskMembers.user_id IN (:...assignee)', { assignee: query.assignee })
        .getQuery();

      tasks.andWhere(`task.id IN ${subQuery}`);
    }

    if (query?.taskLeader?.length) {
      tasks.andWhere('taskLeader.id in (:...taskLeader)', {
        taskLeader: query.taskLeader,
      });
    }

    if (query?.createdBy?.includes('Automatically')) {
      tasks.andWhere('task.recurring is true');
    }

    if (query?.createdBy?.length) {
      if (query.createdBy.includes('Automatically')) {
        query.createdBy = query.createdBy.filter((item) => item !== 'Automatically');
        if (query.createdBy.length) {
          tasks.andWhere('user.id in (:...createdBy)', {
            createdBy: query.createdBy,
          });
        }
      } else {
        tasks.andWhere('user.id in (:...createdBy)', {
          createdBy: query.createdBy,
        });
      }
    }

    let dueStatuses: any[] = query.status?.reduce((acc, status) => {
      if (status.endsWith('_overdue')) {
        acc.push(status.replace('_overdue', ''));
      }
      return acc;
    }, []);

    let nonDueStatuses: any[] = query.status?.filter((status) => !status.endsWith('_overdue'));
    tasks.andWhere(
      new Brackets((qb) => {
        let hasAddedCondition = false;
        let complitedStatus = false;
        if (nonDueStatuses?.length) {
          if (nonDueStatuses.includes(TaskStatusEnum.COMPLETED)) {
            nonDueStatuses = nonDueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
            complitedStatus = true;
          }
          if (nonDueStatuses?.length) {
            qb.where('task.status IN (:...nonDueStatuses) AND task.dueDate >= :todayDate', {
              nonDueStatuses: nonDueStatuses,
              todayDate: moment().format('YYYY-MM-DD').toString(),
            });
          }

          if (complitedStatus) {
            qb.andWhere(
              'task.status=:comStatus AND Date(task.dueDate) >= Date(task.statusUpdatedAt)',
              {
                comStatus: TaskStatusEnum.COMPLETED,
              },
            );
          }
          hasAddedCondition = true;
        }

        let dueComplited = false;
        if (dueStatuses?.length) {
          if (dueStatuses.includes(TaskStatusEnum.COMPLETED)) {
            dueStatuses = dueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
            dueComplited = true;
          }

          if (hasAddedCondition) {
            if (dueStatuses?.length) {
              qb.orWhere('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
                dueStatuses: dueStatuses,
                todayDatee: moment().format('YYYY-MM-DD').toString(),
              });
            }

            if (dueComplited) {
              qb.orWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
                dueComplited: TaskStatusEnum.COMPLETED,
              });
            }
          } else {
            if (dueStatuses?.length) {
              qb.where('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
                dueStatuses: dueStatuses,
                todayDatee: moment().format('YYYY-MM-DD').toString(),
              });
            }

            if (dueComplited) {
              qb.andWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
                dueComplited: TaskStatusEnum.COMPLETED,
              });
            }
          }
        }
      }),
    );

    if (query?.serviceDetails) {
      tasks.andWhere('service.id = :serviceId', {
        serviceId: query?.serviceDetails,
      });
    }

    if (query?.billingType?.length) {
      tasks.andWhere('task.paymentStatus in (:...paymentStatus)', {
        paymentStatus: query.billingType,
      });
    }

    if (query?.billable?.length) {
      let billableType = [];
      if (query?.billable?.includes('billable')) {
        billableType.push(true);
      }
      if (query?.billable?.includes('nonbillable')) {
        billableType.push(false);
      }
      tasks.andWhere('task.billable in (:...billable)', {
        billable: billableType,
      });
    }

    if (query?.priority?.length) {
      tasks.andWhere('task.priority in (:...priority)', {
        priority: query.priority,
      });
    }

    if (query.financialYear?.length) {
      tasks.andWhere('task.financialYear in (:...financialYear)', {
        financialYear: query.financialYear,
      });
    }

    if (query?.tags?.length) {
      tasks.andWhere('labels.id in (:...labels)', {
        labels: query.tags,
      });
    }

    if (query.completedBy?.length) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` (select
          user_id from task_status ts
          where ts.task_id = task.id
          and ts.status = "completed"
          order by ts.created_at desc
          limit 1) in (:...completedBy)`,
        { completedBy: query.completedBy },
      );
    }

    if (query.search) {
      tasks.andWhere(
        `(
          task.name like :search or
          task.taskNumber like :search or
          client.displayName like :search or
          clientGroup.displayName like :search
        )`,
        { search: '%' + query.search + '%' },
      );
    }

    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (query.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    if (query?.removeCompleted) {
      tasks.andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      });
    }

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'expectedCompletionDate',
      dateFilterKey: DateFilterKeys.EXPECTED_COMPLETION_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'statusUpdatedAt',
      dateFilterKey: DateFilterKeys.COMPLETED_ON,
    });

    if (query.offset) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }

    const [t, c] = await Promise.all([tasks.getMany(), tasks.getCount()]);

    const taskIds = t.map((task) => task.id);
    let memberMap = new Map();

    if (taskIds.length > 0) {
      const members = await Task.createQueryBuilder('task')
        .select(['task.id', 'members.id', 'members.fullName'])
        .leftJoin('task.members', 'members')
        .where('task.id IN (:...taskIds)', { taskIds })
        .getMany();

      memberMap = new Map(members.map((task) => [task.id, task.members]));
    }

    // Assign only required member fields to each task
    const tasksWithMembers = t.map((task) => ({
      ...task,
      members: memberMap.get(task.id) || [],
    }));

    return { t: tasksWithMembers, c };
    // return [tasksWithMembers, c]
  }

  async findApprovalTasksCount(userId: number, query: FindTasksQuery) {
    const count = await this.findApprovalTasks(userId, query);
    return count?.c;
  }

  async getGroupTasks(userId: number, query: FindTasksQuery) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;
    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.status',
        'task.recurringStatus',
        'task.createdAt',
        'task.approvalStatus',
        'task.recurring',
        'task.billable',
        'task.createdDate',
        'task.paymentStatus',
        'task.feeAmount',
        'task.financialYear',
      ])
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoin('task.user', 'user');

    tasks
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('approvalLevels.user', 'approvalLevelsUsers');

    tasks.where('task.organization.id = :organization', { organization: user.organization.id });

    if (!query.subTasks) {
      tasks.andWhere('task.parentTask IS NULL');
    }
    if (query?.mainTaks && query.mainTaks) {
      tasks.andWhere('task.parentTask IS NOT NULL');
    }
    if (assigned) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('approvalLevelsUsers.id = :userId AND task.status = :statusApproval', {
            userId,
            statusApproval: TaskStatusEnum.UNDER_REVIEW,
          })
            .orWhere('members.id=:userId', { userId })
            .orWhere('taskLeader.id=:userId', { userId });
        }),
      );
    }

    tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    });

    tasks.andWhere(
      new Brackets((qb) =>
        qb
          .where('task.status IN (:...statuses)', {
            statuses: [
              TaskStatusEnum.TODO,
              TaskStatusEnum.IN_PROGRESS,
              TaskStatusEnum.ON_HOLD,
              TaskStatusEnum.UNDER_REVIEW,
            ],
          })
          .orWhere('task.status = :completedStatus', {
            completedStatus: TaskStatusEnum.COMPLETED,
          })
          .andWhere('task.statusUpdatedAt >= :todayDate', {
            todayDate: moment().subtract(15, 'days').toDate(),
          }),
      ),
    );

    const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoinAndSelect('clientGroup.organization', 'organization')
      .leftJoinAndSelect('clientGroup.clients', 'clients')
      .where('clientGroup.id = :id', { id: query?.clientGroup })
      .andWhere('organization.id = :organization', { organization: user.organization.id })
      .getOne();
    const clientGroupIDs = clientGroup?.clients.map((item) => item.id);

    tasks.andWhere(
      new Brackets((qb) => {
        qb.where('clientGroup.id = :clientGroupId', { clientGroupId: query?.clientGroup });

        if (clientGroupIDs && clientGroupIDs.length > 0) {
          qb.orWhere('client.id IN (:...clientIds)', { clientIds: clientGroupIDs });
        }
      }),
    );

    tasks.addOrderBy('task.createdDate', 'DESC').addOrderBy('task.id', 'DESC');

    if (query.search) {
      tasks.andWhere(
        `(
          task.name like :search or
          task.taskNumber like :search or
          client.displayName like :search or
          clientGroup.displayName like :search
        )`,
        { search: '%' + query.search + '%' },
      );
    }
    if (query?.removeCompleted) {
      tasks.andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      });
    }

    if (query.offset) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }

    let result = await tasks.getManyAndCount();

    return result;
  }

  async findSubTask(userId: number, query: FindTasksQuery) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;
    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.financialYear',
        'task.status',
        'task.recurringStatus',
        'task.createdAt',
        'task.approvalStatus',
        'task.recurring',
        'task.billable',
        'task.createdDate',
        'task.paymentStatus',
        'task.feeAmount',
        'task.taskStartDate',
        'task.expectedCompletionDate',
        'parentTask.taskNumber',
        'parentTask.name',
        'task.clientGroup',
        'client.id',
        'client.displayName',
        'client.panNumber',
        'client.gstNumber',
        'client.clientId',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroup.panNumber',
        'clientGroup.gstNumber',
        'clientGroup.clientId',
        'members.id',
        'members.fullName',
        'user.id',
      ])
      .leftJoin('task.members', 'members')
      .leftJoin('task.parentTask', 'parentTask')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.user', 'user');

    tasks
      .leftJoin('task.approvalProcedures', 'approvalProcedures')
      .leftJoin('approvalProcedures.approval', 'approval')
      .leftJoin('approval.approvalLevels', 'approvalLevels')
      .leftJoin('approvalLevels.user', 'approvalLevelsUsers');
    if (query.export) {
      tasks
        .leftJoinAndSelect('task.category', 'category')
        .leftJoinAndSelect('task.subCategory', 'subCategory');
    }

    tasks.where('task.organization.id = :organization', { organization: user.organization.id });

    if (!query.subTasks) {
      tasks.andWhere('task.parentTask IS NOT NULL');
    }
    if (assigned) {
      const subQuery = tasks
        .subQuery()
        .select('taskMembers.task_id')
        .from('task_members_user', 'taskMembers')
        .where('taskMembers.user_id = :userId')
        .getQuery();

      tasks.andWhere(
        new Brackets((qb) => {
          qb.where(`task.id IN ${subQuery}`, { userId });
        }),
      );
    }

    tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    });
    tasks.andWhere(
      new Brackets((qb) =>
        qb
          .where('task.status IN (:...statuses)', {
            statuses: [
              TaskStatusEnum.TODO,
              TaskStatusEnum.IN_PROGRESS,
              TaskStatusEnum.ON_HOLD,
              TaskStatusEnum.UNDER_REVIEW,
            ],
          })
          .orWhere('task.status = :completedStatus', {
            completedStatus: TaskStatusEnum.COMPLETED,
          })
          .andWhere('task.statusUpdatedAt >= :todayDate', {
            todayDate: moment().subtract(15, 'days').toDate(),
          }),
      ),
    );
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        dueDate: 'task.dueDate',
        priority: 'task.priority',
        status: 'task.status',
        financialYear: 'task.financialYear',
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.addOrderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.addOrderBy('task.createdDate', 'DESC').addOrderBy('task.id', 'DESC');
    }

    if (query.completedOn && !query.createdOn) {
      tasks.andWhere(`task.status=:status`, { status: TaskStatusEnum.COMPLETED });
    }

    if (query.client && query.client) {
      tasks.andWhere('task.client.id = :client', {
        client: query.client,
      });
    }

    if (query.clientGroup && query.clientGroup) {
      tasks.andWhere('task.clientGroup.id = :clientGroup', {
        clientGroup: query.clientGroup,
      });
    }

    if (query?.assignee?.length) {
      const subQuery = tasks
        .subQuery()
        .select('taskMembers.task_id')
        .from('task_members_user', 'taskMembers')
        .where('taskMembers.user_id IN (:...assignee)', { assignee: query.assignee })
        .getQuery();

      tasks.andWhere(`task.id IN ${subQuery}`);
    }

    if (query?.createdBy?.includes('Automatically')) {
      tasks.andWhere('task.recurring is true');
    }

    const dueStatuses: any[] = query.status?.reduce((acc, status) => {
      if (status.endsWith('_overdue')) {
        acc.push(status.replace('_overdue', ''));
      }
      return acc;
    }, []);
    const nonDueStatuses: any[] = query.status?.filter((status) => !status.endsWith('_overdue'));
    tasks.andWhere(
      new Brackets((qb) => {
        let hasAddedCondition = false;
        if (nonDueStatuses?.length) {
          qb.where('task.status IN (:...nonDueStatuses) AND task.dueDate >= :todayDate', {
            nonDueStatuses: nonDueStatuses,
            todayDate: moment().format('YYYY-MM-DD').toString(),
          });
          hasAddedCondition = true;
        }
        if (dueStatuses?.length) {
          if (hasAddedCondition) {
            qb.orWhere('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
              dueStatuses: dueStatuses,
              todayDatee: moment().format('YYYY-MM-DD').toString(),
            });
          } else {
            qb.where('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
              dueStatuses: dueStatuses,
              todayDatee: moment().format('YYYY-MM-DD').toString(),
            });
          }
        }
      }),
    );

    if (query?.billingType?.length) {
      tasks.andWhere('task.paymentStatus in (:...paymentStatus)', {
        paymentStatus: query.billingType,
      });
    }

    if (query?.billable?.length) {
      let billableType = [];
      if (query?.billable?.includes('billable')) {
        billableType.push(true);
      }
      if (query?.billable?.includes('nonbillable')) {
        billableType.push(false);
      }
      tasks.andWhere('task.billable in (:...billable)', {
        billable: billableType,
      });
    }

    if (query?.priority?.length) {
      tasks.andWhere('task.priority in (:...priority)', {
        priority: query.priority,
      });
    }

    if (query?.financialYear?.length) {
      tasks.andWhere('task.financialYear in (:...financialYear)', {
        financialYear: query.financialYear,
      });
    }

    if (query?.tags?.length) {
      tasks.andWhere('labels.id in (:...labels)', {
        labels: query.tags,
      });
    }

    if (query?.completedBy?.length) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` (select
          user_id from task_status ts
          where ts.task_id = task.id
          and ts.status = "completed"
          order by ts.created_at desc
          limit 1) in (:...completedBy)`,
        { completedBy: query.completedBy },
      );
    }

    if (query.search) {
      tasks.andWhere(
        `(
          task.name like :search or
          task.taskNumber like :search or
          client.displayName like :search or 
          clientGroup.displayName like :search
        )`,
        { search: '%' + query.search + '%' },
      );
    }

    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (query.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    if (query?.removeCompleted) {
      tasks.andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      });
    }

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'expectedCompletionDate',
      dateFilterKey: DateFilterKeys.EXPECTED_COMPLETION_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'statusUpdatedAt',
      dateFilterKey: DateFilterKeys.COMPLETED_ON,
    });

    if (query.offset) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }

    let result = await tasks.getManyAndCount();

    return result;
  }
  async exportTaskViewReport(userId: number, query: FindTasksQuery) {
    let tasks = await this.find(userId, query);
    // console.log(tasks[0]);
    // let rows = tasks[0]?.map((task) => {
    //   console.log(task,"Taslkkkkkkkkkkkkkkkkkkkkkkkkkk");
    //   return {
    //     // 'Task ID': task.taskNumber,
    //     // 'Client': task?.client?.displayName,
    //     // 'Task Name': task?.name,
    //     // 'Due Date': task.dueDate,
    //     // 'Priority': getTitle(task?.priority),
    //     // 'Status': getTitle(task?.status),
    //     // 'User': task?.members.map((assignee) => assignee.fullName).join(', '),
    //   };
    // });

    // if (rows !== undefined && rows.length) {
    //   const worksheet = xlsx.utils.json_to_sheet(rows);
    //   const workbook = xlsx.utils.book_new();
    //   xlsx.utils.book_append_sheet(workbook, worksheet, 'users');
    //   let file = xlsx.write(workbook, { type: 'buffer' });
    //   return file;
    // } else {
    //   throw new BadRequestException('No Data for Export');
    // }
  }

  async exportClientTaskReport(userId: number, query: FindTasksQuery) {
    let tasks: any = await this.find(userId, query);

    if (!tasks?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Pending Task');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Task Id', key: 'taskId' },
      { header: 'Financial Year', key: 'financialYear' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Task Status', key: 'taskStatus' },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Task Leader', key: 'taskLeader' },
      { header: 'Priority', key: 'priority' },
      { header: 'Members', key: 'members' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks[0].forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        taskId: task.taskNumber,
        financialYear: task.financialYear,
        taskName: task?.name,
        taskStatus: getTitle(task?.status),
        startDate: formatDate(task?.createdDate),
        dueDate: formatDate(task?.dueDate),
        taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
        priority: getTitle(task?.priority),
        members: task?.members.map((assignee) => assignee.fullName).join(', '),
      };

      const row = worksheet.addRow(rowData);
      const priorityCell = row.getCell('priority');
      switch (rowData.priority?.toLowerCase()) {
        case 'high':
          priorityCell.font = { color: { argb: 'FB0505' }, bold: true };
          break;
        case 'medium':
          priorityCell.font = { color: { argb: 'F17F23' }, bold: true };
          break;
        case 'low':
          priorityCell.font = { color: { argb: '019335' }, bold: true };
          break;
        default:
          priorityCell.font = { color: { argb: '64B5F6' }, bold: true };
          break;
      }

      const statusCell = row.getCell('taskStatus');
      switch (rowData.taskStatus?.toLowerCase()) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true };
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true };
          break;
        case 'on hold':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      // Calculate maximum width for each column
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportClientGroupTaskReport(userId: number, query: FindTasksQuery) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getGroupTasks(userId, newQuery);

    if (!tasks.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Pending Task');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client / Client Group', key: 'client' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Financial Year', key: 'financialYear' },
      { header: 'Task Name', key: 'taskName' },
      { header: 'Due Date', key: 'dueDate' },
      { header: 'Task Leaders', key: 'taskLeader' },
      { header: 'Status', key: 'status' },
      { header: 'Members', key: 'members' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    tasks[0].forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        client: task?.client ? task?.client.displayName : task?.clientGroup?.displayName,
        taskId: task.taskNumber,
        financialYear: task.financialYear,
        taskName: task?.name,
        startDate: formatDate(task?.createdDate),
        dueDate: formatDate(task?.dueDate),
        taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
        status: getTitle(task?.status),
        members: task?.members.map((assignee) => assignee.fullName).join(', '),
      };

      const row = worksheet.addRow(rowData);

      const billingStatusCell = row.getCell('status');
      if (rowData.status?.toUpperCase() === 'BILLED') {
        billingStatusCell.font = { color: { argb: 'FF00B050' }, bold: true };
      } else {
        billingStatusCell.font = { color: { argb: 'FFFF0000' }, bold: true };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findondemand(userId: number, query: FindTasksQuery) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoin('task.members', 'taskMembers')
      .where('task.organization.id = :organization', { organization: user.organization.id })
      .andWhere('task.parentTask IS NULL')
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere(
        new Brackets((qb) =>
          qb
            .where('task.status IN (:...statuses)', {
              statuses: [
                TaskStatusEnum.TODO,
                TaskStatusEnum.IN_PROGRESS,
                TaskStatusEnum.ON_HOLD,
                TaskStatusEnum.UNDER_REVIEW,
              ],
            })
            .orWhere('task.status = :completedStatus', {
              completedStatus: TaskStatusEnum.COMPLETED,
            })
            .andWhere('task.statusUpdatedAt >= :todayDate', {
              todayDate: moment().subtract(15, 'days').toDate(),
            }),
        ),
      );

    if (assigned) {
      tasks.andWhere('taskMembers.id = :userId', { userId });
    }

    if (query.client) {
      tasks.andWhere('task.client.id = :client', {
        client: query.client,
      });
    }

    if (query.category) {
      tasks.andWhere('category.id in (:...categories)', {
        categories: query.category,
      });
    }

    if (query.subCategory) {
      tasks.andWhere('subCategory.id in (:...subCategories)', {
        subCategories: query.subCategory,
      });
    }

    if (query.clientCategory) {
      tasks.andWhere('client.category in (:...clientCategories)', {
        clientCategories: query.clientCategory,
      });
    }

    if (query.clientSubCategory) {
      tasks.andWhere('client.subCategory in (:...clientSubCategories)', {
        clientSubCategories: query.clientSubCategory,
      });
    }

    if (query.assignee) {
      tasks.andWhere('members.id in (:...assignee)', {
        assignee: query.assignee,
      });
    }

    if (query.createdBy) {
      tasks.andWhere('user.id in (:...createdBy)', {
        createdBy: query.createdBy,
      });
    }

    if (query.status) {
      tasks.andWhere('task.status in (:...status)', {
        status: query.status,
      });
    }

    if (query.priority) {
      tasks.andWhere('task.priority in (:...priority)', {
        priority: query.priority,
      });
    }

    if (query.financialYear) {
      tasks.andWhere('task.financialYear in (:...financialYear)', {
        financialYear: query.financialYear,
      });
    }

    if (query.tags) {
      tasks.andWhere('labels.id in (:...labels)', {
        labels: query.tags,
      });
    }

    if (query.completedBy) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` (select
          user_id from task_status ts
          where ts.task_id = task.id
          and ts.status = "completed"
          order by ts.created_at desc
          limit 1) in (:...completedBy)`,
        { completedBy: query.completedBy },
      );
    }

    if (query.search) {
      tasks.andWhere(
        `(
          task.name like :search
        )`,
        { search: '%' + query.search + '%' },
      );
    }

    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (query.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    tasks.orderBy('task.id', 'DESC');
    tasks.skip(query.limit * (query.page - 1));
    tasks.take(query.limit);

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    let data = await tasks.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async findcalender(userId: number, query: FindTasksQuery) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    if (!all && !assigned) {
      return null;
    }

    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.name',
        'task.dueDate',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName',
        'task.taskNumber',
        'task.recurring',
        'task.status',
        'task.billable',
        'parentTask.id',
        'taskMember.id',
        'taskMember.fullName',
        'taskLeader.id',
        'taskLeader.fullName',
      ])
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.parentTask', 'parentTask')
      .leftJoin('task.members', 'taskMember')
      .leftJoin('task.taskLeader', 'taskLeader')
      .where('task.organization.id = :organization', { organization: user.organization.id })
      .andWhere('(task.recurringStatus IS NULL OR task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      });

    if (query.completedOn && !query.createdOn) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
    }

    if (assigned) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('taskMember.id = :userId', { userId }).orWhere('taskLeader.id = :userId', {
            userId,
          });
        }),
      );
    }

    if (query.client) {
      tasks.andWhere('task.client.id = :client', {
        client: query.client,
      });
    }

    if (query.category || query.subCategory) {
      tasks.leftJoin('task.category', 'category');
      if (query.category) {
        tasks.andWhere('category.id IN (:...categories)', {
          categories: query.category,
        });
      }
    }

    if (query.subCategory) {
      tasks.leftJoin('task.subCategory', 'subCategory');
      tasks.andWhere('subCategory.id IN (:...subCategories)', {
        subCategories: query.subCategory,
      });
    }

    if (query.clientCategory) {
      tasks.andWhere('client.category IN (:...clientCategories)', {
        clientCategories: query.clientCategory,
      });
    }

    if (query.clientSubCategory) {
      tasks.andWhere('client.subCategory IN (:...clientSubCategories)', {
        clientSubCategories: query.clientSubCategory,
      });
    }

    if (query.assignee) {
      tasks.andWhere('taskMember.id IN (:...assignee)', {
        assignee: query.assignee,
      });
    }

    if (query.createdBy) {
      tasks.leftJoin('task.user', 'user');
      tasks.andWhere('user.id IN (:...createdBy)', {
        createdBy: query.createdBy,
      });
    }

    if (query.status) {
      tasks.andWhere('task.status IN (:...status)', {
        status: query.status,
      });
    }

    if (query.priority) {
      tasks.andWhere('task.priority IN (:...priority)', {
        priority: query.priority,
      });
    }

    if (query.financialYear) {
      tasks.andWhere('task.financialYear IN (:...financialYear)', {
        financialYear: query.financialYear,
      });
    }

    if (query.tags) {
      tasks.leftJoin('task.labels', 'labels');
      tasks.andWhere('labels.id IN (:...labels)', {
        labels: query.tags,
      });
    }

    if (query.startDates) {
      const startOfMonth = moment(query.startDates).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query.startDates).endOf('month').format('YYYY-MM-DD');
      tasks.andWhere(`Date(task.due_date) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
    }

    if (query.completedBy) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        `EXISTS (
          SELECT 1 FROM task_status ts
          WHERE ts.task_id = task.id
          AND ts.status = 'completed'
          AND ts.user_id IN (:...completedBy)
          ORDER BY ts.created_at DESC
          LIMIT 1
        )`,
        { completedBy: query.completedBy },
      );
    }

    if (query.search) {
      const searchTerm = `%${query.search}%`;
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('task.name LIKE :search', { search: searchTerm })
            .orWhere('task.taskNumber LIKE :search', { search: searchTerm })
            .orWhere('client.displayName LIKE :search', { search: searchTerm });

          if (query.category || query.subCategory) {
            qb.orWhere('category.name LIKE :search', { search: searchTerm }).orWhere(
              'subCategory.name LIKE :search',
              { search: searchTerm },
            );
          }

          if (query.clientCategory || query.clientSubCategory) {
            qb.orWhere('client.category LIKE :search', { search: searchTerm }).orWhere(
              'client.subCategory LIKE :search',
              { search: searchTerm },
            );
          }
        }),
      );
    }

    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring = true');
      }

      if (query.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring = false');
      }
    }

    const offset = query.limit * (query.page - 1);
    tasks.skip(offset).take(query.limit);

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'statusUpdatedAt',
      dateFilterKey: DateFilterKeys.COMPLETED_ON,
    });

    let result = await tasks.getManyAndCount();

    return {
      totalCount: result[1],
      result: result[0],
      viewAllPermission: all ? 1 : 0,
    };
  }

  filterByDate(args: IFilterByDate) {
    const { query, dateFilterKey, entityKey = 'taskStartDate', tasks } = args;
    let today = this.getDates(DateKeys.TODAY);
    let yesterday = this.getDates(DateKeys.YESTERDAY);
    let thisWeekStart = this.getDates(DateKeys.WEEK_START);
    let thisWeekEnd = this.getDates(DateKeys.WEEK_END);
    let lastWeekStart = this.getDates(DateKeys.LAST_WEEK_START);
    let lastWeekEnd = this.getDates(DateKeys.LAST_WEEK_END);
    let thisMonthStart = this.getDates(DateKeys.MONTH_START);
    let thisMonthEnd = this.getDates(DateKeys.MONTH_END);
    let lastMonthStart = this.getDates(DateKeys.LAST_MONTH_START);
    let lastMonthEnd = this.getDates(DateKeys.LAST_MONTH_END);
    let whereStatement = '';

    const appendStatement = (statement: string) => {
      if (whereStatement) {
        whereStatement += ` or ${statement}`;
      } else {
        whereStatement += statement;
      }
    };

    if (query[dateFilterKey]?.includes(DateFilters.TODAY)) {
      const statement = `Date(task.${entityKey}) = '${today}'`;
      appendStatement(statement);
    }
    if (query[dateFilterKey]?.includes(DateFilters.YESTERDAY)) {
      const statement = `Date(task.${entityKey}) = '${yesterday}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.THIS_WEEK)) {
      const statement = `Date(task.${entityKey}) between '${thisWeekStart}' and '${thisWeekEnd}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.LAST_WEEK)) {
      const statement = `Date(task.${entityKey}) between '${lastWeekStart}' and '${lastWeekEnd}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.THIS_MONTH)) {
      const statemenet = `Date(task.${entityKey}) between '${thisMonthStart}' and '${thisMonthEnd}'`;
      appendStatement(statemenet);
    }

    if (query[dateFilterKey]?.includes(DateFilters.LAST_MONTH)) {
      const statement = `Date(task.${entityKey}) between '${lastMonthStart}' and '${lastMonthEnd}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.OVERDUE)) {
      const statement = `Date(task.${entityKey}) >= '${today}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.CUSTOM)) {
      let customDates = JSON.parse(query.customDates);
      const { startTime, endTime } = dateFormation(
        customDates[dateFilterKey].fromDate,
        customDates[dateFilterKey].toDate,
      );

      const statement = `Date(task.${entityKey})
       between '${startTime}'
       and '${endTime}'`;
      appendStatement(statement);
    }

    if (whereStatement) {
      tasks.andWhere(`(${whereStatement})`);
    }
  }

  getDates(key: DateKeys) {
    const today = moment().format('YYYY-MM-DD');
    const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD');
    const thisWeekStart = moment().startOf('week').add(1, 'days').format('YYYY-MM-DD');
    const thisWeekEnd = moment().endOf('week').add(1, 'days').format('YYYY-MM-DD');
    const lastWeekStart = moment()
      .subtract(1, 'weeks')
      .startOf('week')
      .add(1, 'days')
      .format('YYYY-MM-DD');
    const lastWeekEnd = moment()
      .subtract(1, 'weeks')
      .endOf('week')
      .add(1, 'days')
      .format('YYYY-MM-DD');
    const thisMonthStart = moment().startOf('month').format('YYYY-MM-DD');
    const thisMonthEnd = moment().endOf('month').format('YYYY-MM-DD');
    const lastMonthStart = moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
    const lastMonthEnd = moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD');

    switch (key) {
      case DateKeys.TODAY:
        return today;
      case DateKeys.YESTERDAY:
        return yesterday;
      case DateKeys.WEEK_START:
        return thisWeekStart;
      case DateKeys.WEEK_END:
        return thisWeekEnd;
      case DateKeys.LAST_WEEK_START:
        return lastWeekStart;
      case DateKeys.LAST_WEEK_END:
        return lastWeekEnd;
      case DateKeys.MONTH_START:
        return thisMonthStart;
      case DateKeys.MONTH_END:
        return thisMonthEnd;
      case DateKeys.LAST_MONTH_START:
        return lastMonthStart;
      case DateKeys.LAST_MONTH_END:
        return lastMonthEnd;
      default:
        return '';
    }
  }

  async getAsOptions(userId: number) {
    let user = await User.findOne({ where: { id: userId } });
    let tasks = await createQueryBuilder(Task, 'task')
      .select([
        'task.id as id',
        'task.name as name',
        'client.id as clientId',
        'client.displayName as clientName',
        'task.dueDate as dueDate',
        'members as members',
      ])
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.members', 'members')
      .where('task.organization.id = :organization', { organization: user.organization.id })

      .getRawMany();

    return tasks;
  }

  async getUserTasks(userId: number, query: FindUserTasksDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const {
      limit,
      offset,
      tab,
      dueDate,
      toStartDate,
      fromStartDate,
      status,
      clientId = null,
      clientType = 'CLIENT',
    } = query;
    let tasks = createQueryBuilder(Task, 'task');
    // if (tab !== 'timesheet') {
    //   tasks.leftJoinAndSelect('task.taskStatus', 'taskStatus');
    // }
    if (tab === 'timesheet') {
      if (!query?.client && !clientId) {
        return {
          count: 0,
          result: [],
        };
      }
    }
    tasks
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.taskStatus', 'taskStatus')
      .where('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.organization =:orgId', { orgId: user?.organization?.id });

    if (tab !== 'task') {
      tasks.andWhere('(members.id = :userId OR taskLeader.id = :userId)', { userId });
    } else {
      tasks.andWhere('(members.id = :userId)', { userId });
    }

    if (tab !== 'task') {
      tasks.andWhere('(members.id = :userId OR taskLeader.id = :userId)', { userId });
    } else {
      tasks.andWhere('(members.id = :userId)', { userId });
    }

    if (tab === 'task') {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.TODO}','${TaskStatusEnum.IN_PROGRESS}','${TaskStatusEnum.ON_HOLD}','${TaskStatusEnum.UNDER_REVIEW}')`,
      );
    } else if (tab === 'archives') {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.COMPLETED}','${TaskStatusEnum.TERMINATED}','${TaskStatusEnum.DELETED}')`,
      );
      // } else if (tab === 'timesheet') {
      //   tasks.andWhere(
      //     `task.status IN ('${TaskStatusEnum.TODO}','${TaskStatusEnum.IN_PROGRESS}','${TaskStatusEnum.ON_HOLD}','${TaskStatusEnum.UNDER_REVIEW}', '${TaskStatusEnum.COMPLETED}')`,
      //   );
      // }
    } else if (tab === 'timesheet') {
      // Fetch organization preferences early for use in query
      const organizationPreferences = await OrganizationPreferences.findOne({
        where: { organization: user?.organization?.id },
        order: { id: 'DESC' },
      });
      const prefs: any = organizationPreferences?.taskPreferences || {};

      if (prefs?.isAllowTimesheetCompleteTask) {
        const minCompletedDate = moment()
          .subtract(prefs?.completeTaskDays || 1, 'days')
          .format('YYYY-MM-DD');

        tasks.andWhere(
          `(task.status IN (:...activeStatuses) OR 
            (task.status = :completedStatus AND 
             EXISTS (
               SELECT 1 
               FROM task_status ts 
               WHERE ts.task_id = task.id 
                 AND ts.status = :completedStatus 
                 AND ts.created_at >= :minCompletedDate
               ORDER BY ts.created_at DESC 
               LIMIT 1
             )
            )
          )`,
          {
            activeStatuses: [
              TaskStatusEnum.TODO,
              TaskStatusEnum.IN_PROGRESS,
              TaskStatusEnum.ON_HOLD,
              TaskStatusEnum.UNDER_REVIEW,
            ],
            completedStatus: TaskStatusEnum.COMPLETED,
            minCompletedDate,
          },
        );
      } else {
        tasks.andWhere(`task.status IN (:...activeStatuses)`, {
          activeStatuses: [
            TaskStatusEnum.TODO,
            TaskStatusEnum.IN_PROGRESS,
            TaskStatusEnum.ON_HOLD,
            TaskStatusEnum.UNDER_REVIEW,
          ],
        });
      }
    } else {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.TODO}','${TaskStatusEnum.IN_PROGRESS}','${TaskStatusEnum.ON_HOLD}','${TaskStatusEnum.UNDER_REVIEW}')`,
      );
    }
    if (clientId) {
      if (clientType == 'CLIENT_GROUP') {
        tasks.andWhere('clientGroup.id = :clientId', { clientId });
      } else {
        tasks.andWhere('client.id = :clientId', { clientId });
      }
    }

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        displayName: 'client.displayName',
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        taskStartDate: 'task.taskStartDate',
        dueDate: 'task.dueDate',
        status,
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.id', 'DESC');
    }
    // tasks.andWhere('task.parentTask is null');

    if (query.priority) {
      tasks.andWhere('task.priority = :priorities', { priorities: query?.priority });
    }

    if (query.search) {
      tasks.andWhere('task.name like :search', { search: `%${query.search}%` });
    }

    if (query.client) {
      tasks.andWhere('client.id = :clientId', { clientId: query.client });
    }

    if (query.clientGroup) {
      tasks.andWhere('clientGroup.id = :clientGroupId', { clientGroupId: query.clientGroup });
    }

    if (query.billable && query.billable !== BillableType.ALL) {
      if (query.billable === 'true') {
        tasks.andWhere('task.billable is true');
      } else {
        tasks.andWhere('task.billable is false');
      }
    }

    if (status && status.length) {
      tasks.andWhere(`task.status IN ('${status}')`);
    }

    if (fromStartDate && fromStartDate.length && fromStartDate !== 'null') {
      tasks.andWhere('task.taskStartDate >= :fromDate', {
        fromDate: moment(fromStartDate).format('YYYY-MM-DD'),
      });
    }

    if (toStartDate && toStartDate.length && toStartDate !== 'null') {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: moment(toStartDate).format('YYYY-MM-DD'),
      });
    }

    if (dueDate && dueDate.length && dueDate !== 'null') {
      tasks.andWhere('task.dueDate <= :duedate', { duedate: moment(dueDate).format('YYYY-MM-DD') });
    }

    if (query.taskValue) {
      query.taskValue === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.taskValue === 'sub_task'
        ? tasks.andWhere('task.parentTask is not null')
        : '';
    }

    if (query.subtask && !query.taskValue) {
      tasks.andWhere('task.parentTask is null');
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    tasks.orderBy('task.id', 'DESC');

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async getUesrApprovalTasks(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset, tab, dueDate, toStartDate, fromStartDate, status } = query;
    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .where('approvalLevels.user=:userId', { userId })
      .andWhere('task.organization=:organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      });

    if (tab === 'task') {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.TODO}','${TaskStatusEnum.IN_PROGRESS}','${TaskStatusEnum.ON_HOLD}','${TaskStatusEnum.UNDER_REVIEW}')`,
      );
    } else if (tab === 'archives') {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.COMPLETED}','${TaskStatusEnum.TERMINATED}','${TaskStatusEnum.DELETED}')`,
      );
    } else {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.TODO}','${TaskStatusEnum.IN_PROGRESS}','${TaskStatusEnum.ON_HOLD}','${TaskStatusEnum.UNDER_REVIEW}','${TaskStatusEnum.COMPLETED}')`,
      );
    }

    if (query.search) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('task.name LIKE :search', { search: `%${query.search}%` }).orWhere(
            'client.displayName LIKE :search',
            { search: `%${query.search}%` },
          );
        }),
      );
    }

    if (query.client) {
      tasks.andWhere('client.id = :clientId', { clientId: query.client });
    }

    tasks.andWhere('task.parentTask is null').andWhere('task.processInstanceId is not null');

    if (status && status.length) {
      tasks.andWhere(`task.status IN ('${status}')`);
    }
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        displayName: 'client.displayName',
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        taskStartDate: 'task.taskStartDate',
        dueDate: 'task.dueDate',
        status: 'task.status',
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.id', 'DESC');
    }

    // if (fromStartDate && fromStartDate.length && fromStartDate !== 'null') {
    //   tasks.andWhere('task.taskStartDate >= :fromDate', {
    //     fromDate: moment(fromStartDate).format('YYYY-MM-DD'),
    //   });
    // }

    // if (toStartDate && toStartDate.length && toStartDate !== 'null') {
    //   tasks.andWhere('task.taskStartDate <= :toDate', {
    //     toDate: moment(toStartDate).format('YYYY-MM-DD'),
    //   });
    // }

    // if (dueDate && dueDate.length && dueDate !== 'null') {
    //   tasks.andWhere('task.dueDate <= :duedate', { duedate: moment(dueDate).format('YYYY-MM-DD') });
    // }

    if (query?.fromStartDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', { fromDate: query?.fromStartDate });
    }

    if (query?.toStartDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: query?.toStartDate,
      });
    }

    if (query?.fromDueDate) {
      tasks.andWhere('task.dueDate >= :fromDueDate', { fromDueDate: query?.fromDueDate });
    }

    if (query?.toDueDate) {
      tasks.andWhere('task.dueDate <= :toDueDate', {
        toDueDate: query?.toDueDate,
      });
    }

    if (query?.fromExpectedDate) {
      tasks.andWhere('task.expectedCompletionDate >= :fromExpectedDate', {
        fromExpectedDate: query?.fromExpectedDate,
      });
    }

    if (query?.toExpectedDate) {
      tasks.andWhere('task.expectedCompletionDate <= :toExpectedDate', {
        toExpectedDate: query?.toExpectedDate,
      });
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    // tasks.orderBy('task.id', 'DESC');

    let result = await tasks.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async exportTaskUserReport(userId: number, query: FindUserTasksDto) {
    let tasks = await this.getUserTasks(userId, query);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task (Assignee)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'FY', key: 'financialYear' },
      { header: 'Task ID', key: 'taskNumber' },
      { header: 'Task Type', key: 'taskType' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Task Status', key: 'taskStatus' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        financialYear: task?.financialYear,
        taskNumber: task?.taskNumber,
        taskType: task.recurring ? 'Recurring' : 'Non-Recurring',
        taskName: task?.name,
        taskStatus: getTitle(task?.status),
        startDate: formatDate(task?.taskStartDate),
        dueDate: formatDate(task?.dueDate),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('taskStatus');
      switch (rowData.taskStatus?.toLowerCase()) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true };
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true };
          break;
        case 'on hold':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'deleted':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'terminated':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportApprovalTaskUserReport(userId: number, query: FindUserTasksDto) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks: any = await this.getUesrApprovalTasks(userId, newQuery);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task (Approver)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'FY', key: 'financialYear' },
      { header: 'Task ID', key: 'taskNumber' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Task Type', key: 'taskType' },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'DueDate' },
      { header: 'Task Status', key: 'taskStatus' },
      { header: 'Approval Status', key: 'approvalStatus' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        financialYear: task?.financialYear,
        taskNumber: task?.taskNumber,
        taskName: task?.name,
        taskType: task.recurring ? 'Recurring' : 'Non-Recurring',
        startDate: formatDate(task?.taskStartDate),
        DueDate: formatDate(task?.dueDate),
        taskStatus: getTitle(task?.status),
        approvalStatus: task?.approvalStatus?.[0]['status'],
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('taskStatus');
      switch (rowData.taskStatus?.toLowerCase()) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true };
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true };
          break;
        case 'on hold':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'deleted':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'terminated':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportTaskUserCardReport(userId: number, query: FindUserTasksDto) {
    let tasks = await this.getUserTasks(userId, query);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task (Assignee)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'FY', key: 'financialYear' },
      { header: 'Task ID', key: 'taskNumber' },
      { header: 'Task Type', key: 'taskType' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Task Status', key: 'taskStatus' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        financialYear: task?.financialYear,
        taskNumber: task?.taskNumber,
        taskType: task.recurring ? 'Recurring' : 'Non-Recurring',
        taskName: task?.name,
        taskStatus: getTitle(task?.status),
        startDate: formatDate(task?.taskStartDate),
        dueDate: formatDate(task?.dueDate),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('taskStatus');
      switch (rowData.taskStatus?.toLowerCase()) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true };
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true };
          break;
        case 'on hold':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'deleted':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'terminated':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getUserTaskLeaderTasks(userId: number, query: FindUserTasksDto) {
    const { limit, offset } = query;

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoin('task.taskLeader', 'taskTaskLeader')
      .leftJoinAndSelect('task.client', 'client')
      .where('taskTaskLeader.id = :userId', { userId: query.userId })
      .andWhere('task.parentTask is null')
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere(`task.status NOT IN ('${TaskStatusEnum.DELETED}','${TaskStatusEnum.TERMINATED}')`)
      .andWhere('task.parentTask is null');

    if (query.search) {
      tasks.andWhere('task.name like :search', { search: `%${query.search}%` });
    }

    if (query.client) {
      tasks.andWhere('client.id = :clientId', { clientId: query.client });
    }

    if (query.billable) {
      tasks.andWhere('task.billable is true');
    }
    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    tasks.orderBy('task.id', 'DESC');

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }
  async exportUserTaskLeaderTasksReport(userId: number, query: FindUserTasksDto) {
    let tasks = await this.getUserTaskLeaderTasks(userId, query);
    let rows = tasks.result.map((task) => {
      return {
        'Client': task?.client?.displayName,
        'Task ID': task.taskNumber,
        'Task Type': task.recurring ? 'Recurring' : 'Non-Recurring',
        'Task Name': task?.name,
        'Start Date': formatDate(task.taskStartDate),
        'Statutory Due Date': formatDate(task.dueDate),
        'Status': getTitle(task?.status),
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'users');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }
  async getTaskLeaderTasks(userId: number, query: FindUserTasksDto) {
    const { limit, offset, tab, fromDueDate, toDueDate, status } = query;

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      // .leftJoin('task.taskLeader', 'taskTaskLeader')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .where('taskLeader.id = :userId', { userId: userId })
      .andWhere('task.parentTask is null')
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      });
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        displayName: 'client.displayName',
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        taskStartDate: 'task.taskStartDate',
        dueDate: 'task.dueDate',
        status: 'task.status',
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.id', 'DESC');
    }
    if (tab === 'task') {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.TODO}','${TaskStatusEnum.IN_PROGRESS}','${TaskStatusEnum.ON_HOLD}','${TaskStatusEnum.UNDER_REVIEW}')`,
      );
    } else if (tab === 'archives') {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.COMPLETED}','${TaskStatusEnum.TERMINATED}','${TaskStatusEnum.DELETED}')`,
      );
    } else {
      tasks.andWhere(
        `task.status IN ('${TaskStatusEnum.TODO}','${TaskStatusEnum.IN_PROGRESS}','${TaskStatusEnum.ON_HOLD}','${TaskStatusEnum.UNDER_REVIEW}','${TaskStatusEnum.COMPLETED}')`,
      );
    }

    tasks.andWhere('task.parentTask is null');

    if (query.search) {
      tasks.andWhere('task.name like :search', { search: `%${query.search}%` });
    }

    if (query.client) {
      tasks.andWhere('client.id = :clientId', { clientId: query.client });
    }

    if (query.billable && query.billable !== BillableType.ALL) {
      if (query.billable === BillableType.BILLABLE) {
        tasks.andWhere('task.billable is true');
      } else {
        tasks.andWhere('task.billable is false');
      }
    }

    if (status && status.length) {
      tasks.andWhere(`task.status IN ('${status}')`);
    }

    // if (fromStartDate && fromStartDate.length) {
    //   tasks.andWhere('task.taskStartDate >= :fromDate', {
    //     fromDate: moment(fromStartDate).format('YYYY-MM-DD'),
    //   });
    // }

    // if (toStartDate && toStartDate.length) {
    //   tasks.andWhere('task.taskStartDate <= :toDate', {
    //     toDate: moment(toStartDate).format('YYYY-MM-DD'),
    //   });
    // }

    // if (dueDate && dueDate.length) {
    //   tasks.andWhere('task.dueDate <= :duedate', { duedate: moment(dueDate).format('YYYY-MM-DD') });
    // }
    if (query?.fromStartDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', { fromDate: query?.fromStartDate });
    }

    if (query?.toStartDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: query?.toStartDate,
      });
    }

    if (query?.fromDueDate) {
      tasks.andWhere('task.dueDate >= :fromDueDate', { fromDueDate: query?.fromDueDate });
    }

    if (query?.toDueDate) {
      tasks.andWhere('task.dueDate <= :toDueDate', {
        toDueDate: query?.toDueDate,
      });
    }

    if (query?.fromExpectedDate) {
      tasks.andWhere('task.expectedCompletionDate >= :fromExpectedDate', {
        fromExpectedDate: query?.fromExpectedDate,
      });
    }

    if (query?.toExpectedDate) {
      tasks.andWhere('task.expectedCompletionDate <= :toExpectedDate', {
        toExpectedDate: query?.toExpectedDate,
      });
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async getTaskLeaderUpcomingTasks(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoin('task.taskStatus', 'taskStatus')
      .where('task.organization.id = :organization', { organization: user.organization.id })
      .andWhere('task.parentTask is null')
      .andWhere('(task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.PENDING,
      })
      .andWhere(
        `task.status NOT IN ('${TaskStatusEnum.COMPLETED}', '${TaskStatusEnum.DELETED}', '${TaskStatusEnum.TERMINATED}')`,
      );
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        displayName: 'client.displayName',
        priority: 'task.priority',
        name: 'task.name',
        task_start_date: 'task.taskStartDate',
        due_date: 'task.dueDate',
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.taskStartDate', 'ASC');
    }
    if (query?.taskLeaderId) {
      tasks.andWhere('taskLeader.id = :leaderId', { leaderId: parseInt(query.taskLeaderId) });
    }

    if (query.priority) {
      tasks.andWhere('task.priority = :priorities', { priorities: query?.priority });
    }

    if (query?.recuuringType === 'recurring') {
      tasks.andWhere('task.recurring is true');
    } else if (query?.recuuringType === 'non-recurring') {
      tasks.andWhere('task.recurring is false');
    }

    if (query.search) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('task.taskNumber LIKE :taskNumber', {
            taskNumber: `%${query.search}%`,
          });
          qb.orWhere('task.name LIKE :name', {
            name: `%${query.search}%`,
          });
        }),
      );
    }

    if (query?.fromStartDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', { fromDate: query?.fromStartDate });
    }

    if (query?.toStartDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: query?.toStartDate,
      });
    }

    if (query?.fromDueDate) {
      tasks.andWhere('task.dueDate >= :fromDueDate', { fromDueDate: query?.fromDueDate });
    }

    if (query?.toDueDate) {
      tasks.andWhere('task.dueDate <= :toDueDate', {
        toDueDate: query?.toDueDate,
      });
    }

    if (query?.fromExpectedDate) {
      tasks.andWhere('task.expectedCompletionDate >= :fromExpectedDate', {
        fromExpectedDate: query?.fromExpectedDate,
      });
    }

    if (query?.toExpectedDate) {
      tasks.andWhere('task.expectedCompletionDate <= :toExpectedDate', {
        toExpectedDate: query?.toExpectedDate,
      });
    }

    if (query?.priority) {
      tasks.andWhere('task.priority = :priority', { priority: query?.priority });
    }

    if (query?.offset >= 0) {
      tasks.skip(query?.offset);
    }

    if (query?.limit) {
      tasks.take(query?.limit);
    }

    const data = await tasks.getManyAndCount();

    return data;
  }

  async exportUsercardUpcomingLeaderTasks(userId: number, query: FindUserTasksDto) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getTaskLeaderUpcomingTasks(userId, newQuery);

    if (!tasks[0].length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Upcoming Task (Leader)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'Task Type', key: 'taskType' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Task Status', key: 'taskStatus' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks[0].forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        taskType: task.recurring ? 'Recurring' : 'Non-Recurring',
        taskName: task?.name,
        taskStatus: getTitle(task?.status),
        startDate: formatDate(task?.taskStartDate),
        dueDate: formatDate(task?.dueDate),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('taskStatus');
      switch (rowData.taskStatus?.toLowerCase()) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true };
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true };
          break;
        case 'on hold':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportuserUpcomingLeaderTasks(userId: number, query: FindUserTasksDto) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getTaskLeaderUpcomingTasks(userId, newQuery);

    if (!tasks[0].length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task (Leader)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'Task Type', key: 'taskType' },
      // { header: 'Task ID', key: 'taskNumber' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Priority', key: 'priority' },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Task Status', key: 'taskStatus' },
      { header: 'Task Leader', key: 'taskLeader' },
      { header: 'Members', key: 'members' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks[0].forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        // taskNumber: task?.taskNumber,
        taskType: task.recurring ? 'Recurring' : 'Non-Recurring',
        taskName: task?.name,
        priority: task.priority,
        taskStatus: getTitle(task?.status),
        startDate: formatDate(task?.taskStartDate),
        dueDate: formatDate(task?.dueDate),
        taskLeader: task?.taskLeader?.map((item) => item?.fullName).join(', '),
        members: task?.members?.map((member) => member?.fullName).join(', '),
      };

      const row = worksheet.addRow(rowData);
      const priorityCell = row.getCell('priority');
      switch (rowData.priority?.toLowerCase()) {
        case 'high':
          priorityCell.font = { color: { argb: 'FB0505' }, bold: true };
          break;
        case 'medium':
          priorityCell.font = { color: { argb: 'F17F23' }, bold: true };
          break;
        case 'low':
          priorityCell.font = { color: { argb: '019335' }, bold: true };
          break;
        default:
          priorityCell.font = { color: { argb: '64B5F6' }, bold: true };
          break;
      }
      const statusCell = row.getCell('taskStatus');
      switch (rowData.taskStatus?.toLowerCase()) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true };
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true };
          break;
        case 'on hold':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'deleted':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'terminated':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportTaskLeaderTasksReport(userId: number, query: FindUserTasksDto) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getTaskLeaderTasks(userId, newQuery);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task (Leader)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'FY', key: 'financialYear' },
      { header: 'Task ID', key: 'taskNumber' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Task Status', key: 'taskStatus' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        financialYear: task?.financialYear,
        taskNumber: task?.taskNumber,
        taskType: task.recurring ? 'Recurring' : 'Non-Recurring',
        taskName: task?.name,
        taskStatus: getTitle(task?.status),
        startDate: formatDate(task?.taskStartDate),
        dueDate: formatDate(task?.dueDate),
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('taskStatus');
      switch (rowData.taskStatus?.toLowerCase()) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true };
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true };
          break;
        case 'on hold':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'deleted':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'terminated':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async findOne(id: number, userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user?.role?.permissions?.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user?.role?.permissions?.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    const taskQuery = await createQueryBuilder(Task, 'task')
      .select([
        'task',
        'members.id',
        'members.fullName',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroup.type',
        'user.id',
        'user.fullName',
        'taskLeader.id',
        'taskLeader.fullName',
        'organization.id',
        'approvalProcedures.id',
      ])
      .leftJoin('task.organization', 'organization')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.user', 'user')
      .leftJoin('task.members', 'members')
      .leftJoin('task.taskLeader', 'taskLeader')
      .leftJoin('task.approvalProcedures', 'approvalProcedures')
      .leftJoin('approvalProcedures.approval', 'approval')
      .leftJoin('approval.approvalLevels', 'approvalLevels')
      .leftJoin('approvalLevels.user', 'approvalLevelsUsers')
      .where('task.id = :id', { id })
      .andWhere('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere(
        new Brackets((qb) => {
          if (assigned && user.role.name !== 'Admin') {
            qb.where(
              new Brackets((qb4: any) => {
                const subQuery = qb4
                  .subQuery()
                  .select('task_id')
                  .from('task_task_leader_user', 'taskLeader')
                  .where('taskLeader.user_id = :userId')
                  .getQuery();
                qb4.where(`task.id IN ${subQuery}`, { userId });
              }),
            )
              .orWhere(
                new Brackets((qb2: any) => {
                  const subQuery = qb2
                    .subQuery()
                    .select('task_id')
                    .from('task_members_user', 'taskMembers')
                    .where('taskMembers.user_id = :userId')
                    .getQuery();
                  qb2.where(`task.id IN ${subQuery}`, { userId });
                }),
              )
              .orWhere(
                new Brackets((qb3: any) => {
                  qb3
                    .where('approvalLevelsUsers.id = :userId')
                    .andWhere('task.status = :statusApproval', {
                      userId,
                      statusApproval: TaskStatusEnum.UNDER_REVIEW,
                    });
                }),
              );
          }
        }),
      );
    if (query?.clientId) {
      taskQuery.andWhere('client.id =:clientId', { clientId: query?.clientId });
    }

    let task = await taskQuery.getOne();
    let taskData = await createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'parentTask.id',
        'parentTask.name',
        'udinTask.id',
        'parentTaskMembers.id',
        'parentTaskMembers.fullName',
        'category.id',
        'category.name',
        'subCategory.id',
        'subCategory.name',
        'labels.id',
        'labels.name',
        'service.subtaskServices',
        'subTasks.id',
        'subTasks.taskNumber',
        'subTasks.name',
        'subTasks.recurringStatus',
        'subTasks.dueDate',
        'subTasks.priority',
        'subTasks.status',
        'subTaskMembers.id',
        'subTaskMembers.fullName',
        'budgetedHoursUser.id',
        'budgetedHoursUser.fullName',
        'taskStatus.status',
        'taskStatus.createdAt',
        'taskBudgetedHours',
        'expenditure',
      ])
      .leftJoin('task.parentTask', 'parentTask')
      .leftJoin('task.subTasks', 'subTasks')
      .leftJoin('subTasks.members', 'subTaskMembers')
      .leftJoin('task.category', 'category')
      .leftJoin('task.subCategory', 'subCategory')
      .leftJoin('task.labels', 'labels')
      .leftJoin('task.taskBudgetedHours', 'taskBudgetedHours')
      .leftJoin('taskBudgetedHours.user', 'budgetedHoursUser')
      .leftJoin('parentTask.members', 'parentTaskMembers')
      .leftJoin('task.service', 'service')
      .leftJoin('task.expenditure', 'expenditure')
      .leftJoin('task.udinTask', 'udinTask')
      .leftJoin('task.taskStatus', 'taskStatus')
      .where('task.id = :id', { id: task?.id })
      .getOne();

    task.parentTask = taskData?.parentTask;
    task.subTasks = taskData?.subTasks;
    task.category = taskData?.category;
    task.subCategory = taskData?.subCategory;
    task.labels = taskData?.labels;
    task.taskBudgetedHours = taskData?.taskBudgetedHours;
    task.service = taskData?.service;
    task.expenditure = taskData?.expenditure;
    task.udinTask = taskData?.udinTask;
    task.taskStatus = taskData?.taskStatus;

    if (task) {
      const activityData = await createQueryBuilder(Activity, 'activity')
        .where('activity.action = :action', { action: 'Remark Added' })
        .andWhere('activity.typeId =:typeId', { typeId: task.id })
        .getMany();
      task['activity'] = activityData;
      if (task && task.subTasks && !task.parentTask) {
        task.subTasks = task.subTasks.filter(
          (subTask) => subTask.status !== 'deleted' && subTask.status !== 'terminated',
        );
      }
      if (task && task.service && !task.parentTask) {
        if (task.service.subtaskServices) {
          const subtaskNames = task.subTasks.map((subTask) => subTask.name);
          task.service.subtaskServices = task.service.subtaskServices.filter((subtaskService) => {
            return !subtaskNames.includes(subtaskService.name);
          });
        }
      }

      task['budgetedHoursData'] = Math.floor(moment.duration(task.budgetedhours).asHours());
      const remainingDuration = moment
        .duration(task.budgetedhours)
        .subtract(Math.floor(moment.duration(task.budgetedhours).asHours()), 'hours');
      const remainingMinutes = Math.floor(remainingDuration.asMinutes());
      task['budgetedMinutesData'] = remainingMinutes;
      for (let i of task.taskBudgetedHours) {
        i['budgetedHoursData'] = Math.floor(moment.duration(i.budgetedHours).asHours());
        const remainingDuration = moment
          .duration(i.budgetedHours)
          .subtract(Math.floor(moment.duration(i.budgetedHours).asHours()), 'hours');
        const remainingMinutes = Math.floor(remainingDuration.asMinutes());
        i['budgetedMinutesData'] = remainingMinutes;
      }
    }
    return task;
  }

  async getTaskAnalytics(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });
    const completedDays = organizationPreferences?.taskPreferences?.['taskDate'] || 15;
    const date = moment().subtract(completedDays, 'days').format('YYYY-MM-DD HH:mm:ss');
    let sql = `
    select status, count(task.id) as count,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring,
    sum(case when task.priority = 'high' then 1 else 0 end) as high,
    sum(case when task.priority = 'low' then 1 else 0 end) as low,
    sum(case when task.priority = 'medium' then 1 else 0 end) as medium,
    sum(case when task.priority = 'none' then 1 else 0 end) as none
    from task`;

    if (assigned) {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    sql += ` where organization_id = ${user.organization.id} and status not in ('completed')`;

    if (assigned) {
      sql += ` and tmu.user_id = ${userId}`;
    }

    sql += ` and task.parent_task_id is null
    and (task.recurring_status is null or task.recurring_status = 'created')
    and task.status <> ''
    group by task.status;`;

    let tasks = await getManager().query(sql);
    let completedSql = `
    SELECT
        status,
        COUNT(task.id) AS count,
        SUM(CASE WHEN task.recurring = true THEN 1 ELSE 0 END) AS recurring,
        SUM(CASE WHEN task.recurring = false THEN 1 ELSE 0 END) AS non_recurring,
        SUM(CASE WHEN task.priority = 'high' THEN 1 ELSE 0 END) AS high,
        SUM(CASE WHEN task.priority = 'low' THEN 1 ELSE 0 END) AS low,
        SUM(CASE WHEN task.priority = 'medium' THEN 1 ELSE 0 END) AS medium,
        SUM(CASE WHEN task.priority = 'none' THEN 1 ELSE 0 END) AS none
    FROM
        task `;
    if (assigned) {
      completedSql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    completedSql += ` WHERE organization_id = ${user.organization.id} AND status = 'completed'`;

    if (assigned) {
      completedSql += ` and tmu.user_id = ${userId}`;
    }

    completedSql += ` AND status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s')
      AND task.parent_task_id IS NULL
      AND (task.recurring_status IS NULL OR task.recurring_status = 'created')
      AND task.status <> ''
    GROUP BY
      task.status;`;
    let completedtasks = await getManager().query(completedSql);
    tasks = [...tasks, ...completedtasks];

    let recurringTasks = 0;
    let nonRecurringTasks = 0;
    let tasksByStatus: any = {
      todo: 0,
      in_progress: 0,
      under_review: 0,
      on_hold: 0,
      completed: 0,
      terminated: 0,
      deleted: 0,
    };
    let tasksByPriority: any = {
      high: 0,
      low: 0,
      medium: 0,
      none: 0,
    };

    tasks.forEach((task: any) => {
      tasksByStatus[task.status] = +task.count || 0;
      tasksByPriority['high'] += +task['high'] || 0;
      tasksByPriority['low'] += +task['low'] || 0;
      tasksByPriority['medium'] += +task['medium'] || 0;
      tasksByPriority['none'] += +task['none'] || 0;
      recurringTasks += +task.recurring || 0;
      nonRecurringTasks += +task.non_recurring || 0;
    });

    let totalTasks = recurringTasks + nonRecurringTasks;
    let recurringTasksPercentage = Math.round((recurringTasks / totalTasks) * 100);
    let nonRecurringTasksPercentage = Math.round((nonRecurringTasks / totalTasks) * 100);

    return {
      total:
        tasksByStatus.todo +
        tasksByStatus.in_progress +
        tasksByStatus.under_review +
        tasksByStatus.on_hold +
        tasksByStatus.completed +
        tasksByStatus.terminated +
        tasksByStatus.deleted,
      tasksByStatus,
      tasksByPriority,
      recurringTasks,
      nonRecurringTasks,
      recurringTasksPercentage: recurringTasksPercentage || 0,
      nonRecurringTasksPercentage: nonRecurringTasksPercentage || 0,
    };
  }

  normalizeFinancialYear(yearStr: string) {
    // '2023-2024' → '2023-2024'
    // '2024-25' → '2024-2025'
    if (yearStr.includes('-')) {
      const parts = yearStr.split('-');
      const first = parts[0];
      let second = parts[1];

      // If second part is 2 digits, add century
      if (second.length === 2) {
        const century = first.slice(0, 2);
        second = century + second;
      }

      return `${first}-${second}`;
    }
    return yearStr;
  }

  getValidFinancialYear(input: string): string {
    // Pattern: four digits, dash, two digits
    const fyRegex = /^\d{4}-\d{2}$/;

    // If already correct (e.g. '2017-18'), just return
    if (fyRegex.test(input)) return input;

    // If someone gives a long format like '2025-2026', shorten it
    const match = input.match(/^(\d{4})-(\d{4})$/);
    if (match) {
      const start = match[1];
      const end = match[2].slice(2); // take last 2 digits of end year
      return `${start}-${end}`;
    }
  }

  async update(id: number, userId: number, data: UpdateTaskDto) {
    try {
      let task = await Task.findOne({
        where: { id },
        relations: [
          'client',
          'recurringProfile',
          'approvalProcedures',
          'expenditure',
          'service',
          'user',
          'members',
          'approvalProcedures.approval',
          'approvalProcedures.approval.approvalLevels',
          'parentTask',
          'udinTask',
          'udinTask.user',
          'labels',
          'taskLeader',
          'clientGroup',
        ],
      });
      let user = await User.findOne({
        where: { id: userId },
        relations: ['role', 'role.permissions'],
      });

      const oldTaskStatus = task?.status;
      const oldRecurringStatus = task.recurringStatus;
      const newStatus = data?.status;
      const oldPrioirty = task?.priority;
      const oldStartDate = task?.taskStartDate;
      const oldDueDate = task?.dueDate;
      const oldExpectedDate = task?.expectedCompletionDate;
      const oldBillingType = task?.billable;
      const oldFeeType = task?.feeType;
      const oldfee = task?.feeAmount;
      const oldDescription = task?.description;
      const oldTaskMembers = task?.members;
      const oldLables = task?.labels?.map((item) => item?.id);
      const newLabels = data?.labels?.map((item) => item?.id);
      const oldLablesName = task?.labels?.map((item) => item?.name);
      const newLablesName = data?.labels?.map((item) => item?.name);
      const oldLabelsDifference = oldLables?.filter((x) => !newLabels?.includes(x));
      const newLabelsDIfference = newLabels?.filter((x) => !oldLables?.includes(x));
      const olddirectory = task?.directory;
      const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
      const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
      const LeaderIds = data?.taskLeader?.map((item) => item?.id);
      const LeaderName = data?.taskLeader?.map((item) => item?.fullName);
      let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
      let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));

      let subTasks = await Task.find({
        where: { parentTask: id },
      });
      let client;
      let clientGroup;
      if (task?.client) {
        client = await Client.findOne({
          where: { id: task?.client?.id },
          relations: ['clientManagers'],
        });
      } else if (task?.clientGroup) {
        clientGroup = await ClientGroup.findOne({
          where: { id: task?.clientGroup?.id },
          relations: ['clientGroupManagers'],
        });
      }

      let taskCount = await createQueryBuilder(Task, 'task')
        .where('task.recurringProfile = :recurringProfileId', {
          recurringProfileId: task?.recurringProfile?.id,
        })
        .andWhere('task.recurringStatus IN ("created", "terminated")')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .getCount();
      let today = moment().format('YYYY-MM-DD');

      if (
        data.expectedCompletionDate &&
        data.taskStartDate &&
        !(data.expectedCompletionDate >= data.taskStartDate)
      ) {
        throw new BadRequestException(
          'Task Expected Completion Date should greater than Task Start Date',
        );
      }

      // console.log(task);
      if (data?.status === TaskStatusEnum.COMPLETED && data?.status !== oldTaskStatus) {
        if (task?.client?.gstNumber) {
          if (
            ['GSTR-1', 'GSTR-3B', 'GSTR-9 Annual Return by Normal Taxpayer']?.includes(
              task?.service?.name,
            )
          ) {
            const gstrType = {
              'GSTR-1': 'GSTR1',
              'GSTR-3B': 'GSTR3B',
              'GSTR-9 Annual Return by Normal Taxpayer': 'GSTR9',
            };
            // === EXTRACT RETURN PERIOD SMARTLY ===
            let taskRetPrd = '';
            const taskName = task?.name || '';

            let match = taskName?.match(/([A-Za-z]+)\s+(\d{4})\b/);
            if (task?.service?.name === 'GSTR-9 Annual Return by Normal Taxpayer') {
              const fyMatch = taskName?.match(/(\d{4})-(\d{4})/);
              if (!fyMatch) {
                throw new BadRequestException(`Invalid GSTR-9 task name: ${taskName}`);
              }
              const endYear = fyMatch[2]; // 2024
              taskRetPrd = `03${endYear}`; // GSTR-9 filed in March of end year
            } else if (match) {
              const monthName = match[1];
              const yearStr = match[2];
              const monthNum = moment(monthName, 'MMMM', true).format('MM');
              if (monthNum === 'Invalid date') {
                throw new BadRequestException(`Invalid month name: ${monthName}`);
              }
              taskRetPrd = `${monthNum}${yearStr}`;
            } else {
              // 2. Try Quarterly: "Q2 (July - September) 2025"
              match = taskName.match(/Q(\d)\s*\([^)]*\)\s*(\d{4})/);
              if (match) {
                const quarter = match[1];
                const yearStr = match[2];

                const quarterMap: Record<string, { month: string; yearOffset: number }> = {
                  '1': { month: '06', yearOffset: 0 }, // Q1 → June
                  '2': { month: '09', yearOffset: 0 }, // Q2 → September
                  '3': { month: '12', yearOffset: 0 }, // Q3 → December
                  '4': { month: '03', yearOffset: 1 }, // Q4 → March (next year)
                };

                const config = quarterMap[quarter];
                if (!config) {
                  throw new BadRequestException(`Invalid quarter: Q${quarter}`);
                }

                const month = config?.month;
                const finalYear = (parseInt(yearStr) + config?.yearOffset).toString();
                taskRetPrd = `${month}${finalYear}`;
              }
            }

            // 3. Final validation
            if (!taskRetPrd || taskRetPrd.length !== 6) {
              throw new BadRequestException(
                'Unable to determine GST return period from task name. Please check task naming format.',
              );
            }

            // === END: RETURN PERIOD EXTRACTION ===
            const filedReturns = await createQueryBuilder(ReturnsData, 'returnsData')
              .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
              .leftJoin('gstrRegister.organization', 'organization')
              .leftJoinAndSelect('gstrRegister.client', 'client')
              .where('organization.id = :organizationId', {
                organizationId: user?.organization?.id,
              })
              .andWhere('client.id = :clientId', { clientId: task?.client?.id })
              .andWhere('returnsData.rtntype = :rtntype', {
                rtntype: gstrType[task?.service?.name],
              })
              .andWhere('returnsData.retPrd = :retPrd', { retPrd: taskRetPrd })
              .andWhere('returnsData.status = :status', { status: 'Filed' })
              .getOne();
            if (filedReturns) {
              if (filedReturns?.status === 'Filed') {
                // console.log('Allow task complete');
              } else {
                const dataGstr: any = {
                  financialYear: this.getValidFinancialYear(task?.financialYear),
                  clientId: task?.client?.id,
                  gstNumber: task?.client?.gstNumber,
                  clientGroupId: task?.clientGroup?.id,
                  type: task?.client?.registrationType,
                };
                const newData = this.promiseService.syncClientBasedOnTaskComplete(dataGstr, userId);
                if (newData) {
                  const filedReturns = await createQueryBuilder(ReturnsData, 'returnsData')
                    .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
                    .leftJoin('gstrRegister.organization', 'organization')
                    .leftJoinAndSelect('gstrRegister.client', 'client')
                    .where('organization.id = :organizationId', {
                      organizationId: user?.organization?.id,
                    })
                    .andWhere('client.id = :clientId', { clientId: task?.client?.id })
                    .andWhere('returnsData.rtntype = :rtntype', {
                      rtntype: gstrType[task?.service?.name],
                    })
                    .andWhere('returnsData.retPrd = :retPrd', { retPrd: taskRetPrd })
                    .andWhere('returnsData.status = :status', { status: 'Filed' })
                    .getOne();

                  if (filedReturns) {
                    if (filedReturns?.status === 'Filed') {
                      // console.log('Allow task complete');
                    } else {
                      throw new BadRequestException(
                        'GST return pending on Portal. File and complete the Task.',
                      );
                    }
                  } else {
                    throw new BadRequestException(
                      'GST return pending on Portal. File and complete the Task.',
                    );
                  }
                } else {
                  throw new BadRequestException(
                    'GST return pending on Portal. File and complete the Task.',
                  );
                }
              }
            } else {
              const dataGstr: any = {
                financialYear: this.getValidFinancialYear(task?.financialYear),
                clientId: task?.client?.id,
                gstNumber: task?.client?.gstNumber,
                clientGroupId: task?.clientGroup?.id,
                type: task?.client?.registrationType,
              };
              const newData: any = await this.promiseService.syncClientBasedOnTaskComplete(
                dataGstr,
                userId,
              );
              const filedReturns = await createQueryBuilder(ReturnsData, 'returnsData')
                .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
                .leftJoin('gstrRegister.organization', 'organization')
                .leftJoinAndSelect('gstrRegister.client', 'client')
                .where('organization.id = :organizationId', {
                  organizationId: user?.organization?.id,
                })
                .andWhere('client.id = :clientId', { clientId: task?.client?.id })
                .andWhere('returnsData.rtntype = :rtntype', {
                  rtntype: gstrType[task?.service?.name],
                })
                .andWhere('returnsData.retPrd = :retPrd', { retPrd: taskRetPrd })
                .andWhere('returnsData.status = :status', { status: 'Filed' })
                .getOne();
              if (filedReturns) {
                if (filedReturns?.status === 'Filed') {
                  // console.log('Allow task complete');
                } else {
                  throw new BadRequestException(
                    'GST return pending on Portal. File and complete the Task.',
                  );
                }
              } else {
                throw new BadRequestException(
                  'GST return pending on Portal. File and complete the Task.',
                );
              }
            }
            console.log('GST Return Task Completion Flow step 8');
          }
        }
      }
      let taskStatusResult;

      if (
        [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ].includes(task.status) &&
        data.status === TaskStatusEnum.COMPLETED &&
        task.processInstanceId
      ) {
        if (!task.approvalStatus?.[0]?.['completed']) {
          taskStatusResult = 'Task should be Approved at all levels before moving to Completed';
        } else if (task.approvalStatus?.[0]?.['completed']) {
          if (task?.udinTask) {
            if (task?.udinTask?.udinNumber && task?.udinTask?.date && task?.udinTask?.user) {
              task.status = data.status;
              if (oldTaskStatus !== task.status) {
                task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
              }
            } else {
              taskStatusResult = 'Provide UDIN details before moving to Completed';
            }
          } else if (!task.udinTask) {
            task.status = data.status;
            if (oldTaskStatus !== task.status) {
              task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
            }
          }
        }
      } else if (
        task.approvalProcedures &&
        task.processInstanceId &&
        (data.status === TaskStatusEnum.TODO ||
          data.status === TaskStatusEnum.IN_PROGRESS ||
          data.status === TaskStatusEnum.ON_HOLD) &&
        task.status === TaskStatusEnum.UNDER_REVIEW
      ) {
        if (task?.approvalStatus[0]?.['completed']) {
          // taskStatusResult = `As Approvals has been completed you can't move this task to ${data.status}`;
          task.status = data.status;
          if (oldTaskStatus !== task.status) {
            task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          }
        } else if (task?.approvalStatus[0]?.['progress']) {
          taskStatusResult = `You can't move this task once the approval  is in progress`;
        } else {
          taskStatusResult = `You can't move this task once the approval  is in progress`;
        }
        // else {
        //   task.status = data.status;
        //   if (oldTaskStatus !== task.status) {
        //     task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
        //   }
        // }
      } else if (
        subTasks.some((task) =>
          [
            TaskStatusEnum.TODO,
            TaskStatusEnum.IN_PROGRESS,
            TaskStatusEnum.ON_HOLD,
            TaskStatusEnum.UNDER_REVIEW,
          ].includes(task.status),
        ) &&
        data.status === TaskStatusEnum.COMPLETED
      ) {
        taskStatusResult = `All Sub-tasks Should Be Completed Before Completing The Main Task`;
      } else if (!task.processInstanceId && data.status === TaskStatusEnum.UNDER_REVIEW) {
        taskStatusResult =
          "You can't move this task to Under Review as there is no Approvals for this Task";
      } else if (
        task.processInstanceId &&
        !task?.approvalStatus?.[0]['completed'] &&
        data.status === TaskStatusEnum.UNDER_REVIEW
      ) {
        task.status = data.status;
        if (oldTaskStatus !== task.status) {
          task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
        }
        task.approvalStatus = [
          {
            status: `Approval Levels (${task?.approvalProcedures?.approval?.approvalLevels.length})`,
            completed: false,
          },
        ];
        this.eventEmitter.emit(Event_Actions.TASK_STATUS_TO_UNDER_REVIEW, { task, user });
      } else if (data.status === TaskStatusEnum.COMPLETED) {
        if (task?.udinTask) {
          if (task?.udinTask?.udinNumber && task?.udinTask?.date && task?.udinTask?.user) {
            task.status = data.status;
            if (oldTaskStatus !== task.status) {
              task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
            }
          } else {
            taskStatusResult = 'Provide UDIN Details before Completing the Task.';
          }
        } else if (!task.udinTask) {
          task.status = data.status;
          if (oldTaskStatus !== task.status) {
            task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          }
        }
      } else {
        task.status = data.status;
        if (oldTaskStatus !== task.status) {
          task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
        }
      }

      let createInvoice = user.role.permissions.find(
        (permission) => permission.slug === Permissions.CREATE_INVOICE,
      );
      task.description =
        data.description !== null && data.description !== undefined
          ? data.description.trim()
          : data.description;

      if (task.recurringStatus === 'pending' && data.recurringStatus === 'terminated') {
        task.restore = TaskStatusEnum.TODO;
      } else {
        if (data.status === 'terminated' && task.recurringStatus === 'pending') {
          task.restore = TaskStatusEnum.TODO;
        } else {
          task.restore = data.status;
        }
      }
      if (task.recurringStatus !== data.recurringStatus) {
        task.user = user;
        task.createdAt = new Date().toISOString();
      }
      if (
        (data.recurringStatus === 'terminated' || data.recurringStatus === 'created') &&
        !data['profileTerminate'] &&
        task.recurringStatus === 'pending'
      ) {
        task.user = user;
        let service: Service;
        if (task.service) {
          let result = await createQueryBuilder(Service, 'service')
            .leftJoinAndSelect('service.category', 'category')
            .leftJoinAndSelect('service.subCategory', 'subCategory')
            .leftJoinAndSelect('service.checklists', 'checklists')
            .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
            .leftJoinAndSelect('service.subTasks', 'subTasks')
            .leftJoinAndSelect('service.milestones', 'milestones')
            .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
            .where('service.id = :id', { id: task.service.id })
            .getOne();
          service = result;

          task.checklists = service.checklists.map((checklist) => {
            let result = new Checklist();
            delete checklist.id;
            result.name = checklist.name;
            result['userId'] = user.id;
            result.checklistItems = checklist.checklistItems.map((item) => {
              let newItem = new ChecklistItem();
              delete item.id;
              newItem.name = item.name;
              newItem.description = item.description;
              newItem['userId'] = userId;
              return newItem;
            });
            return result;
          });

          task.milestones = service.milestones.map((milestone) => {
            let result = new Milestone();
            delete milestone.id;
            Object.assign(result, milestone);
            return result;
          });

          task.stageOfWorks = service.stageOfWorks.map((stageOfWork) => {
            let result = new StageOfWork();
            delete stageOfWork.id;
            Object.assign(result, stageOfWork);
            return result;
          });
          task.description = service.description;
        }

        if (task.approvalProcedures) {
          const approval = await ApprovalProcedures.findOne({
            where: { id: task.approvalProcedures.id, organization: user.organization.id },
            relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
            select: ['id'],
          });
          // task.approvalProcedures = approval;
          try {
            const data = JSON.stringify({
              processKey: 'genericApprovalProcess',
              metaData: {
                typeOfApproval: 'ATOM_TASK',
                approvalProcessId: `Level${approval?.approval?.approvalLevels.length}ApprovalProcess`,
              },
            });

            let config: any = {
              method: 'post',
              maxBodyLength: Infinity,
              url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
              headers: {
                'Content-Type': 'application/json',
              },
              data: data,
            };

            await axios.request(config).then(async (response) => {
              const processInstanceId = response?.data?.processInstanceId;

              if (processInstanceId)
                task.approvalStatus = [
                  {
                    status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
                    completed: false,
                  },
                ];
              task.processInstanceId = processInstanceId;
              this.approvalProcess(processInstanceId, approval);
            });
          } catch (err) {
            console.log(err);
          }
        }

        task.createdDate = moment().toDate();
        let activity = new Activity();
        activity.action = Event_Actions.TASK_CREATED;
        activity.actorId = user.id;
        activity.type = task?.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        activity.typeId = task?.client ? task?.client?.id : task?.clientGroup?.id;

        activity.remarks = `"${task.taskNumber}" - "${task?.name}" Task created by ${user.fullName}`;
        await activity.save();

        let taskactivity = new Activity();
        taskactivity.action = Event_Actions.TASK_CREATED;
        taskactivity.actorId = user.id;
        taskactivity.type = ActivityType.TASK;
        taskactivity.typeId = task.id;

        taskactivity.remarks = `"${task.taskNumber}" - "${task?.name}" Task created by ${user.fullName}`;
        await taskactivity.save();
        if (!taskStatusResult) {
          let taskStatus = new TaskStatus();
          taskStatus.restore = TaskStatusEnum.TODO;
          taskStatus.status = TaskStatusEnum.TODO;
          taskStatus.task = task;
          taskStatus.user = user;
          await taskStatus.save();
        }

        let completedtask = await Task.findOne({
          where: { id: id },
          relations: [
            'user',
            // 'approvals',
            'checklists',
            'service',
            'organization',
            'taskLeader',
            'client',
            'clientGroup',
            'taskLogHours',
            'recurringProfile',
            'milestones',
            'taskStatus',
            'members',
            'labels',
            'category',
            'subCategory',
            'approvalProcedures',
            'taskBudgetedHours',
            'udinTask',
          ],
        });

        if (completedtask.recurring === true) {
          if (completedtask.frequency !== 'custom') {
            const recurringProfile = completedtask.recurringProfile;
            let service: Service = completedtask.service;
            let nameExt = '';
            let StartDate = '';
            let dueDate = '';
            let expectedCompletionDate = '';
            let taskName = '';
            const splitYear = completedtask.financialYear.split('-');
            const oldYear = String(Number(splitYear[0]) + 1);
            const newYear = String(Number(splitYear[1]) + 1);
            const newFinancialYear = oldYear + '-' + newYear;

            const getStartDate = () => {
              const startDateObj = moment.utc(completedtask.taskStartDate);
              const startDateObjAdd = startDateObj.add(1, 'year');
              StartDate = startDateObjAdd.format('YYYY-MM-DD');
            };

            const getDueDate = () => {
              const dueDateObj = moment.utc(completedtask.dueDate);
              const dueDateObjAdd = dueDateObj.add(1, 'year');
              dueDate = dueDateObjAdd.format('YYYY-MM-DD');
            };

            const getExpectedCompletionDate = () => {
              if (task.expectedCompletionDate) {
                const expectedCompletionDateObj = moment.utc(completedtask.expectedCompletionDate);
                const expectedCompletionDateObjAdd = expectedCompletionDateObj.add(1, 'year');
                expectedCompletionDate = expectedCompletionDateObjAdd.format('YYYY-MM-DD');
              } else {
                expectedCompletionDate = task.expectedCompletionDate;
              }
            };
            const oldTaskName = completedtask.name.split('-');
            if (completedtask.frequency === 'monthly') {
              taskName = oldTaskName.slice(0, -3).join('-');

              const oldTaskDate = oldTaskName[oldTaskName.length - 1];
              const oldYear = String(Number(oldTaskDate.slice(-4)) + 1);
              let oldString = oldTaskName[oldTaskName.length - 1].slice(-4);
              let splitString = oldTaskDate.split(oldString);
              let newDate = splitString.join(oldYear);
              oldTaskName[oldTaskName.length - 1] = newDate;

              nameExt = '- ' + newFinancialYear + ' -' + newDate;

              getStartDate();
              getDueDate();
              getExpectedCompletionDate();
            } else if (completedtask.frequency === 'quarterly') {
              const oldName = completedtask.name.split('-');
              taskName = oldName.slice(0, -4).join('-');
              let newString = String(Number(oldName[oldName.length - 1].slice(-4)) + 1);
              let newDateName = oldName[oldName.length - 1];
              let oldString = oldName[oldName.length - 1].slice(-4);
              let splitString = newDateName.split(oldString);
              let newDate = splitString.join(newString);
              oldName[oldName.length - 1] = newDate;
              let middleName = oldName[oldName.length - 2];
              nameExt = '- ' + newFinancialYear + ' -' + middleName + '-' + newDate;

              getStartDate();
              getDueDate();
              getExpectedCompletionDate();
            } else if (completedtask.frequency === 'half_yearly') {
              const oldName = completedtask.name.split('-');
              taskName = oldName.slice(0, -4).join('-');
              let newString = String(Number(oldName[oldName.length - 1].slice(-4)) + 1);
              let newDateName = oldName[oldName.length - 1];
              let oldString = oldName[oldName.length - 1].slice(-4);
              let splitString = newDateName.split(oldString);
              let newDate = splitString.join(newString);
              oldName[oldName.length - 1] = newDate;
              let middleName = oldName[oldName.length - 2];
              nameExt = '- ' + newFinancialYear + ' -' + middleName + '-' + newDate;

              getStartDate();
              getDueDate();
              getExpectedCompletionDate();
            } else if (completedtask.frequency === 'yearly') {
              const oldName = completedtask.name.split('-');
              taskName = oldName.slice(0, -3).join('-');
              const newDate = oldName[oldName.length - 1];
              nameExt = '- ' + newFinancialYear + ' -' + newDate;

              getStartDate();
              getDueDate();
              getExpectedCompletionDate();
            } else {
              taskName = completedtask.name;

              getStartDate();
              getDueDate();
              getExpectedCompletionDate();
            }

            let pendingTask = new Task();
            pendingTask.name = taskName + nameExt;
            pendingTask.description = completedtask.description;
            pendingTask.category = completedtask.category;
            pendingTask.subCategory = completedtask.subCategory;
            pendingTask.client = completedtask.client;
            pendingTask.clientGroup = completedtask.clientGroup;
            pendingTask.priority = completedtask.priority;
            pendingTask.feeType = completedtask.feeType;
            pendingTask.feeAmount = completedtask.feeAmount;
            pendingTask.budgetedhours = completedtask.budgetedhours;
            pendingTask.labels = completedtask.labels;
            pendingTask.members = completedtask.members;
            pendingTask.taskLeader = completedtask.taskLeader;
            pendingTask.organization = user.organization;
            pendingTask.financialYear = newFinancialYear;
            pendingTask.recurring = true;
            pendingTask.recurringProfile = recurringProfile;
            pendingTask.frequency = completedtask.frequency;
            pendingTask.taskStartDate = StartDate;
            pendingTask.dueDate = dueDate;
            pendingTask.expectedCompletionDate = expectedCompletionDate;
            pendingTask.recurringStatus = TaskRecurringStatus.PENDING;
            pendingTask.restore = TaskStatusEnum.PENDING;
            pendingTask.budgetedhours = completedtask.budgetedhours;
            pendingTask.taskLeader = completedtask.taskLeader;
            pendingTask.isUdin = completedtask?.isUdin ? true : false;

            if (service) {
              pendingTask.name = service.name + nameExt;
              pendingTask.description = service.description;
              pendingTask.category = completedtask.category;
              pendingTask.subCategory = completedtask.subCategory;
              pendingTask.service = service;
            }
            if (completedtask.approvalProcedures) {
              const approval = await ApprovalProcedures.findOne({
                where: {
                  id: completedtask.approvalProcedures.id,
                  organization: user.organization.id,
                },
                relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
                select: ['id'],
              });
              pendingTask.approvalProcedures = approval;
            }
            pendingTask['userId'] = user.id;
            await Task.save(pendingTask);
            for (let item of completedtask?.taskBudgetedHours) {
              let userData = await User.findOne({
                where: { id: item.user.id },
                relations: ['organization'],
              });
              let budgetedHours = new BudgetedHours();
              budgetedHours.status = BudgetedHourStatus.ACTIVE;
              budgetedHours.budgetedHours = item['budgetedHours'];
              budgetedHours.organization = user.organization;
              budgetedHours.task = pendingTask;
              budgetedHours.user = userData;
              await budgetedHours.save();
            }
            if (completedtask?.isUdin) {
              let udintask = new UdinTask();
              udintask.task = pendingTask;
              udintask.udinTaskStatus = UdinTaskStatus.ACTIVE;
              udintask.userType = userType.ORGANIZATION;
              udintask.organization = user.organization;
              udintask.recurringProfile = recurringProfile;
              await udintask.save();
            }

            function generateRRTId(id: number) {
              if (id < 10000) {
                return 'RRT' + id.toString().padStart(4, '0');
              }
              return 'RRT' + id;
            }
            let recurringCount = await createQueryBuilder(Task, 'task')
              .leftJoin('task.organization', 'organization')
              .where('organization.id = :id', { id: user.organization.id })
              .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%RRT%' })
              .getCount();
            task.taskNumber = generateRRTId(recurringCount + 1);
          }
        }
      }
      if (task?.approvalProcedures?.id !== data?.approvalProcedures?.id) {
        if (data?.approvalProcedures?.id) {
          const approval = await ApprovalProcedures.findOne({
            where: { id: data?.approvalProcedures?.id, organization: user.organization.id },
            relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
            select: ['id'],
          });
          task.approvalProcedures = approval;
        } else {
          // task.approvalProcedures = null;
        }
      }

      task.remarks = data.remarks;
      task.members = data.members;
      task.directory =
        data.directory !== null && data.directory !== undefined
          ? data.directory.trim()
          : data.directory;
      if (data.status !== TaskStatusEnum.TERMINATED && data.status !== TaskStatusEnum.DELETED) {
        task.restore = data.status;
      }

      task.recurringStatus = data.recurringStatus;
      if (
        moment(data.taskStartDate).format('YYYY-MM-DD') !=
          moment(task.taskStartDate).format('YYYY-MM-DD') &&
        task.recurringStatus === TaskRecurringStatus.PENDING
      ) {
        if (moment(data.taskStartDate).format('YYYY-MM-DD') <= today) {
          task.recurringStatus = TaskRecurringStatus.CREATED;
          task.createdDate = moment().toDate();
          task.user = user;
          let service: Service;
          if (task.service) {
            let result = await createQueryBuilder(Service, 'service')
              .leftJoinAndSelect('service.category', 'category')
              .leftJoinAndSelect('service.subCategory', 'subCategory')
              .leftJoinAndSelect('service.checklists', 'checklists')
              .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
              .leftJoinAndSelect('service.subTasks', 'subTasks')
              .leftJoinAndSelect('service.milestones', 'milestones')
              .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
              .where('service.id = :id', { id: task.service.id })
              .getOne();
            service = result;

            task.description = service.description;

            task.checklists = service.checklists.map((checklist) => {
              let result = new Checklist();
              delete checklist.id;
              result.name = checklist.name;
              result['userId'] = user.id;
              result.checklistItems = checklist.checklistItems.map((item) => {
                let newItem = new ChecklistItem();
                delete item.id;
                newItem.name = item.name;
                newItem.description = item.description;
                newItem['userId'] = userId;
                return newItem;
              });
              return result;
            });

            task.milestones = service.milestones.map((milestone) => {
              let result = new Milestone();
              delete milestone.id;
              Object.assign(result, milestone);
              return result;
            });

            task.stageOfWorks = service.stageOfWorks.map((stageOfWork) => {
              let result = new StageOfWork();
              delete stageOfWork.id;
              Object.assign(result, stageOfWork);
              return result;
            });
          }

          function generateRRTId(id: number) {
            if (id < 10000) {
              return 'RRT' + id.toString().padStart(4, '0');
            }
            return 'RRT' + id;
          }

          function generateNRTId(id: number) {
            if (id < 10000) {
              return 'NRT' + id.toString().padStart(4, '0');
            }
            return 'NRT' + id;
          }

          let recurringCount = await createQueryBuilder(Task, 'task')
            .leftJoin('task.organization', 'organization')
            .where('organization.id = :id', { id: user.organization.id })
            .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%RRT%' })
            .getCount();

          let nonRecurringCount = await createQueryBuilder(Task, 'task')
            .leftJoin('task.organization', 'organization')
            .where('organization.id = :id', { id: user.organization.id })
            .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%NRT%' })
            .getCount();

          if (task.recurring) {
            task.taskNumber = generateRRTId(recurringCount + 1);
          } else if (!task.parentTask) {
            task.taskNumber = generateNRTId(nonRecurringCount + 1);
          }

          if (task.approvalProcedures) {
            const approval = await ApprovalProcedures.findOne({
              where: { id: task.approvalProcedures.id, organization: user.organization.id },
              relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
              select: ['id'],
            });
            try {
              const data = JSON.stringify({
                processKey: 'genericApprovalProcess',
                metaData: {
                  typeOfApproval: 'ATOM_TASK',
                  approvalProcessId: `Level${approval?.approval?.approvalLevels.length}ApprovalProcess`,
                },
              });

              let config: any = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
                headers: {
                  'Content-Type': 'application/json',
                },
                data: data,
              };

              await axios.request(config).then(async (response) => {
                const processInstanceId = response?.data?.processInstanceId;

                if (processInstanceId)
                  task.approvalStatus = [
                    {
                      status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
                      completed: false,
                    },
                  ];
                task.processInstanceId = processInstanceId;
                this.approvalProcess(processInstanceId, approval);
              });
            } catch (err) {
              console.log(err);
            }
          }
          if (!taskStatusResult) {
            let taskStatus = new TaskStatus();
            taskStatus.restore = TaskStatusEnum.TODO;
            taskStatus.status = TaskStatusEnum.TODO;
            taskStatus.task = task;
            taskStatus.user = user;
            await taskStatus.save();
          }

          let completedtask = await Task.findOne({
            where: { id: id },
            relations: [
              'user',
              // 'approvals',
              'checklists',
              'service',
              'organization',
              'taskLeader',
              'client',
              'clientGroup',
              'taskLogHours',
              'recurringProfile',
              'milestones',
              'taskStatus',
              'members',
              'labels',
              'category',
              'subCategory',
              'taskBudgetedHours',
              'udinTask',
            ],
          });
          if (completedtask.recurring === true) {
            if (completedtask.frequency !== 'custom') {
              const recurringProfile = completedtask.recurringProfile;
              let service: Service = completedtask.service;

              let nameExt = '';
              let StartDate = '';
              let dueDate = '';
              let expectedCompletionDate = '';
              let taskName = '';
              const splitYear = completedtask.financialYear.split('-');
              const oldYear = String(Number(splitYear[0]) + 1);
              const newYear = String(Number(splitYear[1]) + 1);
              const newFinancialYear = oldYear + '-' + newYear;

              const getStartDate = () => {
                const startDateObj = moment.utc(completedtask.taskStartDate);
                const startDateObjAdd = startDateObj.add(1, 'year');
                StartDate = startDateObjAdd.format('YYYY-MM-DD');
              };

              const getDueDate = () => {
                const dueDateObj = moment.utc(completedtask.dueDate);
                const dueDateObjAdd = dueDateObj.add(1, 'year');
                dueDate = dueDateObjAdd.format('YYYY-MM-DD');
              };

              const getExpectedCompletionDate = () => {
                const expectedCompletionDateObj = moment.utc(completedtask.expectedCompletionDate);
                const expectedCompletionDateObjAdd = expectedCompletionDateObj.add(1, 'year');
                expectedCompletionDate = expectedCompletionDateObjAdd.format('YYYY-MM-DD');
                if (task.expectedCompletionDate) {
                  const expectedCompletionDateObj = moment.utc(
                    completedtask.expectedCompletionDate,
                  );
                  const expectedCompletionDateObjAdd = expectedCompletionDateObj.add(1, 'year');
                  expectedCompletionDate = expectedCompletionDateObjAdd.format('YYYY-MM-DD');
                } else {
                  expectedCompletionDate = task.expectedCompletionDate;
                }
              };
              const oldTaskName = completedtask.name.split('-');
              if (completedtask.frequency === 'monthly') {
                taskName = oldTaskName.slice(0, -3).join('-');

                const oldTaskDate = oldTaskName[oldTaskName.length - 1];
                const oldYear = String(Number(oldTaskDate.slice(-4)) + 1);
                let oldString = oldTaskName[oldTaskName.length - 1].slice(-4);
                let splitString = oldTaskDate.split(oldString);
                let newDate = splitString.join(oldYear);
                oldTaskName[oldTaskName.length - 1] = newDate;

                nameExt = '- ' + newFinancialYear + ' -' + newDate;

                getStartDate();
                getDueDate();
                getExpectedCompletionDate();
              } else if (completedtask.frequency === 'quarterly') {
                const oldName = completedtask.name.split('-');
                taskName = oldName.slice(0, -4).join('-');
                let newString = String(Number(oldName[oldName.length - 1].slice(-4)) + 1);
                let newDateName = oldName[oldName.length - 1];
                let oldString = oldName[oldName.length - 1].slice(-4);
                let splitString = newDateName.split(oldString);
                let newDate = splitString.join(newString);
                oldName[oldName.length - 1] = newDate;
                let middleName = oldName[oldName.length - 2];
                nameExt = '- ' + newFinancialYear + ' -' + middleName + '-' + newDate;

                getStartDate();
                getDueDate();
                getExpectedCompletionDate();
              } else if (completedtask.frequency === 'half_yearly') {
                const oldName = completedtask.name.split('-');
                taskName = oldName.slice(0, -4).join('-');
                let newString = String(Number(oldName[oldName.length - 1].slice(-4)) + 1);
                let newDateName = oldName[oldName.length - 1];
                let oldString = oldName[oldName.length - 1].slice(-4);
                let splitString = newDateName.split(oldString);
                let newDate = splitString.join(newString);
                oldName[oldName.length - 1] = newDate;
                let middleName = oldName[oldName.length - 2];
                nameExt = '- ' + newFinancialYear + ' -' + middleName + '-' + newDate;

                getStartDate();
                getDueDate();
                getExpectedCompletionDate();
              } else if (completedtask.frequency === 'yearly') {
                const oldName = completedtask.name.split('-');
                taskName = oldName.slice(0, -3).join('-');
                const newDate = oldName[oldName.length - 1];
                nameExt = '- ' + newFinancialYear + ' -' + newDate;

                getStartDate();
                getDueDate();
                getExpectedCompletionDate();
              } else {
                taskName = completedtask.name;

                getStartDate();
                getDueDate();
                getExpectedCompletionDate();
              }

              let pendingTask = new Task();
              pendingTask.name = taskName + nameExt;
              pendingTask.description = completedtask.description;
              pendingTask.category = completedtask.category;
              pendingTask.subCategory = completedtask.subCategory;
              pendingTask.client = completedtask.client;
              pendingTask.clientGroup = completedtask.clientGroup;
              pendingTask.priority = completedtask.priority;
              pendingTask.feeType = completedtask.feeType;
              pendingTask.feeAmount = completedtask.feeAmount;
              pendingTask.budgetedhours = completedtask.budgetedhours;
              pendingTask.labels = completedtask.labels;
              pendingTask.members = completedtask.members;
              pendingTask.taskLeader = completedtask.taskLeader;
              pendingTask.organization = user.organization;
              pendingTask.financialYear = newFinancialYear;
              pendingTask.recurring = true;
              pendingTask.recurringProfile = recurringProfile;
              pendingTask.frequency = completedtask.frequency;
              pendingTask.taskStartDate = StartDate;
              pendingTask.dueDate = dueDate;
              pendingTask.expectedCompletionDate = expectedCompletionDate;
              pendingTask.recurringStatus = TaskRecurringStatus.PENDING;
              pendingTask.restore = TaskStatusEnum.PENDING;
              pendingTask.budgetedhours = completedtask.budgetedhours;
              pendingTask.taskLeader = completedtask.taskLeader;
              pendingTask.isUdin = completedtask?.isUdin ? true : false;

              if (service) {
                pendingTask.name = service.name + nameExt;
                pendingTask.description = service.description;
                pendingTask.category = completedtask.category;
                pendingTask.subCategory = completedtask.subCategory;
                pendingTask.service = service;
              }
              if (task.approvalProcedures) {
                const approval = await ApprovalProcedures.findOne({
                  where: { id: task.approvalProcedures.id, organization: user.organization.id },
                  relations: [
                    'approval',
                    'approval.approvalLevels',
                    'approval.approvalLevels.user',
                  ],
                  select: ['id'],
                });
                pendingTask.approvalProcedures = approval;
              }
              pendingTask['userId'] = user.id;
              await Task.save(pendingTask);
              for (let item of completedtask?.taskBudgetedHours) {
                let userData = await User.findOne({
                  where: { id: item.user.id },
                  relations: ['organization'],
                });
                let budgetedHours = new BudgetedHours();
                budgetedHours.status = BudgetedHourStatus.ACTIVE;
                budgetedHours.budgetedHours = item['budgetedHours'];
                budgetedHours.organization = user.organization;
                budgetedHours.task = pendingTask;
                budgetedHours.user = userData;
                await budgetedHours.save();
              }

              if (completedtask?.isUdin) {
                let udintask = new UdinTask();
                udintask.task = pendingTask;
                udintask.udinTaskStatus = UdinTaskStatus.ACTIVE;
                udintask.userType = userType.ORGANIZATION;
                udintask.organization = user.organization;
                udintask.recurringProfile = recurringProfile;
                await udintask.save();
              }
            }
          }

          let activity = new Activity();
          activity.action = Event_Actions.TASK_CREATED;
          activity.actorId = user.id;
          activity.type = task?.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
          activity.typeId = task?.client ? task?.client.id : task?.clientGroup?.id;

          activity.remarks = `"${task.taskNumber}" - "${task?.name}" Task created by ${user.fullName}`;
          await activity.save();
        }
      }
      task.priority = data.priority;
      task.feeType = data.feeType;
      task.feeAmount = data.billable ? data.feeAmount : 0;
      task.budgetedhours = data.budgetedhours;
      task.taskLeader = data.taskLeader;
      if (data['createdNow']) {
        task.taskStartDate = moment().format('YYYY-MM-DD');
      } else {
        task.taskStartDate = data.taskStartDate;
      }
      task.dueDate = data.dueDate;
      task.category = data.category;
      task.subCategory = data.subCategory;
      task.labels = data.labels;
      task.expectedCompletionDate = data.expectedCompletionDate;
      task.financialYear = data.financialYear;
      task.paymentStatus = data.paymentStatus;
      task.billable = data.billable;

      if (oldTaskStatus !== newStatus && !taskStatusResult) {
        let taskStatus = new TaskStatus();
        taskStatus.restore = newStatus;
        taskStatus.status = newStatus;
        taskStatus.task = task;
        taskStatus.user = user;
        await taskStatus.save();

        if (oldRecurringStatus !== TaskRecurringStatus.PENDING) {
          this.eventEmitter.emit(Event_Actions.TASK_STATUS_UPDATED, {
            userId,
            task: task,
            oldTaskStatus: oldTaskStatus,
            newStatus: newStatus,
            user: user,
          });
        }
      }
      if (
        data.recurringStatus === TaskRecurringStatus.CREATED &&
        !task.parentTask &&
        !Boolean(task.taskNumber) &&
        !task.recurring
      ) {
        let nonRecurringCount = await createQueryBuilder(Task, 'task')
          .leftJoin('task.organization', 'organization')
          .where('organization.id = :id', { id: user.organization.id })
          .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%NRT%' })
          .getCount();

        function generateNRTId(id: number) {
          if (id < 10000) {
            return 'NRT' + id.toString().padStart(4, '0');
          }
          return 'NRT' + id;
        }
        task.taskNumber = generateNRTId(nonRecurringCount + 1);
      }
      if (
        data.recurringStatus === TaskRecurringStatus.CREATED &&
        task.parentTask &&
        !Boolean(task.taskNumber) &&
        !task.recurring
      ) {
        let nonRecurringCount = await createQueryBuilder(Task, 'task')
          .leftJoin('task.organization', 'organization')
          .where('organization.id = :id', { id: user.organization.id })
          .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%SBT%' })
          .getCount();
        function generateSBTId(id: number) {
          if (id < 10000) {
            return 'SBT' + id.toString().padStart(4, '0');
          }
          return 'SBT' + id;
        }
        task.taskNumber = generateSBTId(nonRecurringCount + 1);
      }

      task['userId'] = user.id;
      task['oldTaskMembers'] = oldTaskMembers;
      task['oldPrioirty'] = oldPrioirty;
      task['oldDueDate'] = oldDueDate;
      task['oldRecurringStatus'] = oldRecurringStatus;
      task['oldTaskStatus'] = oldTaskStatus;
      await task.save();

      if (oldPrioirty !== data.priority) {
        this.eventEmitter.emit(Event_Actions.PRIORITY_UPDATED, {
          task: task,
          oldPriority: oldPrioirty,
          user: user,
        });
      }
      if (task?.taskStartDate && oldStartDate !== task?.taskStartDate) {
        this.eventEmitter.emit(Event_Actions.START_DATE_UPDATED, {
          task: task,
          oldStartDate: oldStartDate,
          user: user,
        });
      }
      if (task.dueDate && oldDueDate !== task.dueDate) {
        this.eventEmitter.emit(Event_Actions.STATUTORY_DUE_DATE_CHANGED, {
          task: task,
          oldDueDate: oldDueDate,
          user: user,
        });
      }
      if (oldExpectedDate !== task.expectedCompletionDate) {
        this.eventEmitter.emit(Event_Actions.EXPECTED_COMPLETEION_DATE_CHANGED, {
          task: task,
          oldExpectedDate: oldExpectedDate,
          user: user,
        });
      }
      if (task.billable && oldBillingType !== task.billable) {
        this.eventEmitter.emit(Event_Actions.BILLING_TYPE_CHANGED, {
          task: task,
          oldBillingType: oldBillingType,
          user: user,
        });
      }
      if (task.feeType && oldFeeType !== task.feeType) {
        this.eventEmitter.emit(Event_Actions.FEE_TYPE_UPDATED, {
          task: task,
          oldFeeType: oldFeeType,
          user: user,
        });
      }
      if (oldfee !== task.feeAmount) {
        this.eventEmitter.emit(Event_Actions.FEE_UPDATED, {
          task: task,
          oldfee: oldfee,
          user: user,
        });
      }
      if (task.description && oldDescription !== task.description) {
        this.eventEmitter.emit(Event_Actions.DESCRIPTION_UPDATED, {
          task: task,
          oldDescription: oldDescription,
          user: user,
        });
      }
      if (oldLabelsDifference?.length || newLabelsDIfference?.length) {
        this.eventEmitter.emit(Event_Actions.LABELS_UPDATED, {
          task: task,
          oldLablesName: oldLablesName,
          newLablesName: newLablesName,
          user: user,
        });
      }
      if (olddirectory !== task?.directory) {
        this.eventEmitter.emit(Event_Actions.DIRECTORY_UPDATED, {
          task: task,
          oldDirectory: olddirectory,
          newDirectory: task.directory,
          user: user,
        });
      }
      if (leadersdifference?.length || taskleaderdifference?.length) {
        let taskactivity = new Activity();
        taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
        taskactivity.actorId = user.id;
        taskactivity.type = ActivityType.TASK;
        taskactivity.typeId = task.id;
        taskactivity.remarks = `Past Task Leaders: ${
          taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
        }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
        await taskactivity.save();
      }
      if (data['lastRecurrringTaskTerminate']) {
        let activity = new Activity();
        activity.action = Event_Actions.RECURRING_PROFILE_TERMINATED;
        activity.actorId = user.id;
        activity.type = task.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        activity.typeId = task.client ? task?.client?.id : task?.clientGroup?.id;
        activity.remarks = `"${
          task?.service?.name || task?.recurringProfile?.name
        }" Recurring Profile Terminated by ${user.fullName}`;
        await activity.save();
      }
      const newTaskStatus = task.status;
      const invoicePopUp =
        oldTaskStatus !== TaskStatusEnum.COMPLETED &&
        newTaskStatus === TaskStatusEnum.COMPLETED &&
        task.billable &&
        task.paymentStatus === PaymentStatusEnum.UNBILLED &&
        (!!createInvoice || user.role.name === 'Admin');
      const proformaInvoicePopUp =
        oldTaskStatus !== TaskStatusEnum.COMPLETED &&
        newTaskStatus === TaskStatusEnum.COMPLETED &&
        task.billable &&
        task.proformaStatus === ProformaTaskStatus.NOT_GENERATED &&
        (!!createInvoice || user.role.name === 'Admin');
      let clientManagersIsNot = false;
      let clientIsDeletedOrNot = true;
      if (client) {
        clientManagersIsNot = Boolean(
          client?.clientManagers?.filter((item) => item?.id === userId).length,
        );
      } else if (clientGroup) {
        clientManagersIsNot = Boolean(
          clientGroup?.clientGroupManagers?.filter((item) => item?.id === userId).length,
        );
      }
      if (client) {
        clientIsDeletedOrNot = client?.status === UserStatus.DELETED ? false : true;
      } else if (clientGroup) {
        clientIsDeletedOrNot = clientGroup?.status === UserStatus.DELETED ? false : true;
      }
      return {
        task,
        clientManagersIsNot,
        taskStatusResult,
        invoicePopUp,
        proformaInvoicePopUp,
        clientIsDeletedOrNot,
      };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async createSubTask(userId: number, taskId: number, data: CreateTaskDto) {
    let clients = await Client.find({ where: { id: In(data.client) } });
    let clientGroup = null;
    if (data.clientGroup) {
      clientGroup = await ClientGroup.findOne({ where: { id: data.clientGroup['value'] } });
    }
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let taskLeader = await User.find({
      where: { id: In(data.taskLeader), type: UserType.ORGANIZATION },
    });
    let members = await User.find({ where: { id: In(data.members), type: UserType.ORGANIZATION } });
    let labels = await Label.find({ where: { id: In(data.labels) } });
    let category = await Category.findOne({ where: { id: data.category } });
    let sCategory = await Category.findOne({ where: { id: data.subCategory } });
    let service: Service;
    let appHier: ApprovalProcedures;

    if (data.serviceType === ServiceType.STANDARD) {
      let result = await createQueryBuilder(Service, 'service')
        .leftJoinAndSelect('service.category', 'category')
        .leftJoinAndSelect('service.subCategory', 'subCategory')
        .leftJoinAndSelect('service.checklists', 'checklists')
        .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
        .leftJoinAndSelect('service.subTasks', 'subTasks')
        .leftJoinAndSelect('service.milestones', 'milestones')
        .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
        .where('service.id = :id', { id: data.service })
        .getOne();
      service = result;
    }

    let existingNumber = await createQueryBuilder(Task, 'task')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.service', 'service')
      .where('task.financialYear = :financialYear', { financialYear: data.financialYear });

    if (service) {
      existingNumber.andWhere('service.id = :id', { id: data.service });
    } else {
      existingNumber.andWhere('task.name LIKE :searchString', { searchString: `%${data.name}%` });
    }

    if (data?.client?.length) {
      existingNumber.andWhere('client.id IN (:...clientId)', { clientId: data.client });
    }

    if (data?.clientGroup) {
      existingNumber.andWhere('clientGroup.id = :clientGroupId', {
        clientGroupId: data.clientGroup['value'],
      });
    }

    const taskCheck = await existingNumber.getOne();

    if (taskCheck && data?.taskCreationCheck) {
      throw new BadRequestException('Task Already Exists');
    }

    const args = {
      userId: userId,
      data: data,
      user: user,
      taskLeader: taskLeader,
      members: members,
      labels: labels,
      category: category,
      subCategory: sCategory,
      service: service,
      appHier: appHier,
    };

    try {
      if (clients.length) {
        for (let client of clients) {
          if (data.taskType === TaskType.RECURRING) {
            await this.createRecurringTask({ ...args, client, clientGroup: null });
          }

          if (data.taskType === TaskType.NON_RECURRING) {
            await this.createNonRecurringTask({ ...args, client, clientGroup: null });
          }
        }
      } else {
        let client = null;
        if (data.taskType === TaskType.RECURRING) {
          await this.createRecurringTask({ ...args, client, clientGroup });
        }

        if (data.taskType === TaskType.NON_RECURRING) {
          await this.createNonRecurringTask({ ...args, client, clientGroup });
        }
      }
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException('Internal server error');
    }
  }

  async updateStatus(id: number, userId: number, body: UpdateStatusBody) {
    try {
      const { restore, status, sourceItemsOrder, destinationItemsOrder } = body;
      let taskLogHour = await LogHour.find({ where: { task: id }, relations: ['user'] });
      if (status === 'completed') {
        if (taskLogHour.length) {
          const lastTaskItem = taskLogHour[taskLogHour.length - 1];
          if (lastTaskItem.status !== 'stopped') {
            let taskLogHourLast = await LogHour.findOne({
              where: { id: lastTaskItem.id },
              relations: ['user'],
            });
            taskLogHourLast.status = TimerStatus.STOPPED;
            taskLogHourLast.completedDate = moment().format('YYYY-MM-DD');
            await taskLogHourLast.save();
          }
        }
      }
      let user = await User.findOne({ where: { id: userId } });

      let task = await Task.findOne({
        where: { id },
        relations: ['client', 'user', 'members', 'taskLeader', 'approvals', 'clientGroup'],
      });

      let assignedMember = task.members.some((member) => member.id === userId);
      let taskLeader = task.taskLeader.filter((item) => item.id === userId);

      if (!assignedMember && !taskLeader.length) {
        throw new ForbiddenException('You are not allowed to move this task');
      }

      task.status = status;
      task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      task.restore = status;
      // task.bhallocation = JSON.stringify(task.bhallocation);
      task['userId'] = user.id;
      await task.save();

      let taskStatus = new TaskStatus();
      taskStatus.status = status;
      taskStatus.restore = status;
      taskStatus.task = task;
      taskStatus.user = user;
      await taskStatus.save();

      await this.reorder(sourceItemsOrder);
      await this.reorder(destinationItemsOrder);

      this.eventEmitter.emit(Event_Actions.TASK_STATUS_UPDATED, {
        userId,
        clientId: task?.client?.id,
        task,
        user: user,
        body,
      });

      return { success: true };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async reorder(items: number[]) {
    try {
      let data = [];
      items.forEach((item, index) => {
        data.push({ id: item, order: index + 1 });
      });
      await Task.save(data);
      return { success: true };
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async restoreTask(userId: number, taskId: number, body: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let task = await Task.findOne({
      where: { id: taskId },
      relations: ['client', 'service', 'parentTask', 'subTasks', 'members', 'clientGroup'],
    });
    if (!user) throw new NotFoundException('User not found');
    if (!task) throw new NotFoundException('Task not found');
    if (task.parentTask) {
      if (
        task.parentTask.status === TaskStatusEnum.TERMINATED ||
        task.parentTask.status === TaskStatusEnum.DELETED
      ) {
        throw new BadRequestException(
          `Please Restore Main Task (${task.parentTask.taskNumber}) Before restoring Subtask`,
        );
      }
    }
    if (task?.client?.status === 'DELETED') {
      throw new BadRequestException(
        `Ensure the Client/Clients Group is Active before attempting to restore any of their deleted/terminated tasks. Restore the Client/Clients Group, followed by the task !`,
      );
    }
    if (task?.clientGroup?.status === 'DELETED') {
      throw new BadRequestException(
        `Ensure the Client/Clients Group is Active before attempting to restore any of their deleted/terminated tasks. Restore the Client/Clients Group, followed by the task !`,
      );
    }
    const targetStatus = task.restore ?? TaskStatusEnum.TODO;
    this.eventEmitter.emit(Event_Actions.TASK_RESTORED, {
      userId,
      oldTaskStatus: task.status,
      newTaskStatus: targetStatus,
      clientId: task.client?.id,
      task,
    });
    task.status = task.restore == null ? TaskStatusEnum.TODO : task.restore;
    task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
    if (task.recurringStatus === 'terminated') {
      task.recurringStatus = TaskRecurringStatus.CREATED;
      if (!task.taskNumber && !task.recurring && !task.parentTask) {
        let count = await Task.count({
          where: {
            organization: {
              id: user.organization.id,
            },
            taskNumber: Like('%NRT%'),
          },
        });
        function generateNRTId(id: number) {
          if (id < 10000) {
            return 'NRT' + id.toString().padStart(4, '0');
          }
          return 'NRT' + id;
        }
        task.taskNumber = generateNRTId(count + 1);
      } else if (!task.taskNumber && task.recurring && !task.parentTask) {
        let count = await Task.count({
          where: {
            organization: {
              id: user.organization.id,
            },
            taskNumber: Like('%RRT%'),
          },
        });
        function generateRRTId(id: number) {
          if (id < 10000) {
            return 'RRT' + id.toString().padStart(4, '0');
          }
          return 'RRT' + id;
        }
        task.taskNumber = generateRRTId(count + 1);
      } else if (!task.taskNumber && !task.recurring && task.parentTask) {
        let count = await Task.count({
          where: {
            organization: {
              id: user.organization.id,
            },
            taskNumber: Like('%SBT%'),
          },
        });
        function generateSBTId(id: number) {
          if (id < 10000) {
            return 'SBT' + id.toString().padStart(4, '0');
          }
          return 'SBT' + id;
        }
        task.taskNumber = generateSBTId(count + 1);
      }

      task.createdDate = moment().toDate();

      if (task.service) {
        let service = await createQueryBuilder(Service, 'service')
          .leftJoinAndSelect('service.category', 'category')
          .leftJoinAndSelect('service.subCategory', 'subCategory')
          .leftJoinAndSelect('service.checklists', 'checklists')
          .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
          .leftJoinAndSelect('service.subTasks', 'subTasks')
          .leftJoinAndSelect('service.milestones', 'milestones')
          .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
          .where('service.id = :id', { id: task.service.id })
          .getOne();

        if (task.recurringStatus === TaskRecurringStatus.CREATED) {
          task.description = service.description;
          task.checklists = service.checklists.map((checklist) => {
            let result = new Checklist();
            delete checklist.id;
            result.name = checklist.name;
            result.task = task;
            result['userId'] = user.id;
            result.checklistItems = checklist.checklistItems.map((item) => {
              let newItem = new ChecklistItem();
              delete item.id;
              newItem.name = item.name;
              newItem.description = item.description;
              newItem['userId'] = userId;
              return newItem;
            });
            return result;
          });

          task.milestones = service.milestones.map((milestone) => {
            let result = new Milestone();
            delete milestone.id;
            Object.assign(result, milestone);
            return result;
          });
          task.stageOfWorks = service.stageOfWorks.map((stageOfWork) => {
            let result = new StageOfWork();
            delete stageOfWork.id;
            Object.assign(result, stageOfWork);
            return result;
          });
        }
      }
    }
    if (body?.taskDeletedData) {
      if (!body?.members?.length) {
        throw new BadRequestException('Please add Atleast One Member !');
      }

      const budgetedHours = await createQueryBuilder(BudgetedHours, 'taskBudgetedHours')
        .leftJoinAndSelect('taskBudgetedHours.task', 'task')
        .leftJoinAndSelect('taskBudgetedHours.user', 'user')
        .where('task.id = :taskId', { taskId: taskId })
        .getMany();

      let oldUserIds = budgetedHours.map((item) => item?.user?.id);

      const oldBudgetedHours = task.budgetedhours;

      let newUserIds = body.members.map((item) => item?.id);

      const oldUsersNotInNew = oldUserIds.filter((userId) => !newUserIds.includes(userId));

      if (body?.budgetedHoursInSeconds) {
        task.budgetedhours = body?.budgetedHoursInSeconds;

        for (let item of body?.members) {
          let userData = await User.findOne({
            where: { id: item?.id },
            relations: ['organization'],
          });

          if (oldUserIds.includes(item?.id)) {
            const hours = await BudgetedHours.findOne({
              where: { id: item?.taskBudgetedHours?.id },
            });

            if (hours) {
              hours.budgetedHours = item?.taskBudgetedHours?.userBudgetedHoursInSeconds;
              hours.status = BudgetedHourStatus.ACTIVE;
              hours.client = task?.client;
              await hours.save();
            } else {
              const oldInactive = await BudgetedHours.findOne({
                where: { task: task, user: userData },
              });
              oldInactive.budgetedHours = item?.taskBudgetedHours?.userBudgetedHoursInSeconds;
              oldInactive.status = BudgetedHourStatus.ACTIVE;
              oldInactive.client = task?.client;
              oldInactive.save();
            }
          } else {
            let budgetedHours = new BudgetedHours();
            budgetedHours.status = BudgetedHourStatus.ACTIVE;
            budgetedHours.budgetedHours = item?.taskBudgetedHours['userBudgetedHoursInSeconds'];
            budgetedHours.organization = user?.organization;
            budgetedHours.task = task;
            budgetedHours.user = userData;
            budgetedHours.client = task?.client;
            await budgetedHours.save();
          }
        }

        if (oldUsersNotInNew.length) {
          await createQueryBuilder(BudgetedHours, 'taskBudgetedHours')
            .update(BudgetedHours)
            .set({ status: BudgetedHourStatus.INACTIVE })
            .where('task.id = :taskId', { taskId: body?.taskId })
            .andWhere('user.id IN (:...userIds)', { userIds: oldUsersNotInNew })
            .execute();
        }
      } else {
        task.budgetedhours = 0;

        const budgetedHours = await createQueryBuilder(BudgetedHours, 'taskBudgetedHours')
          .leftJoinAndSelect('taskBudgetedHours.task', 'task')
          .leftJoinAndSelect('taskBudgetedHours.user', 'user')
          .where('task.id = :taskId', { taskId: body?.taskId })
          .getMany();

        for (let i of budgetedHours) {
          const DeltingBudgetedHours = await BudgetedHours.findOne({ where: { id: i?.id } });
          DeltingBudgetedHours.budgetedHours = 0;
          DeltingBudgetedHours.status = BudgetedHourStatus.INACTIVE;
          DeltingBudgetedHours.save();
        }
      }
      const oldHours = Math.floor(moment.duration(oldBudgetedHours).asHours());
      const remainingDuration = moment
        .duration(oldBudgetedHours)
        .subtract(Math.floor(moment.duration(oldBudgetedHours).asHours()), 'hours');
      const remainingMinutes = Math.floor(remainingDuration.asMinutes());
      const oldMinutes = remainingMinutes;

      const newHours = Math.floor(moment.duration(body?.budgetedHoursInSeconds).asHours());
      const newremainingDuration = moment
        .duration(body?.budgetedHoursInSeconds)
        .subtract(Math.floor(moment.duration(body?.budgetedHoursInSeconds).asHours()), 'hours');
      const newremainingMinutes = Math.floor(newremainingDuration.asMinutes());
      const newMinutes = newremainingMinutes;

      if (body?.budgetedHoursInSeconds) {
        if (parseInt(body?.budgetedHoursInSeconds) === Number(oldBudgetedHours)) {
        } else {
          let taskactivity = new Activity();
          taskactivity.action = Event_Actions.BUDGETED_HOURS_UPDATED;
          taskactivity.actorId = user.id;
          taskactivity.type = ActivityType.TASK;
          taskactivity.typeId = task.id;
          taskactivity.remarks = `Previous Budgeted Hours : ${oldHours} hrs ${oldMinutes} Mns Current Budgeted Hours : ${newHours} Hrs ${newMinutes} Mns`;
          await taskactivity.save();
        }
      } else if (Number(oldBudgetedHours) !== parseInt(body?.budgetedHoursInSeconds)) {
        let taskactivity = new Activity();
        taskactivity.action = Event_Actions.BUDGETED_HOURS_UPDATED;
        taskactivity.actorId = user.id;
        taskactivity.type = ActivityType.TASK;
        taskactivity.typeId = task.id;
        taskactivity.remarks = `Previous Budgeted Hours : ${oldHours} hrs ${oldMinutes} Mns Current Budgeted Hours : 0 hrs 0 Mns`;
        await taskactivity.save();
      }

      const taskMemebersIds = task.members.map((item) => item?.id);
      const taskmembersName = task.members.map((item) => item.fullName);

      const membersIds = body.members.map((item) => item?.id);
      const membersName = body.members.map((item) => item.fullName);

      let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
      let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));

      if (membersdifference.length || taskmembersdifference.length) {
        let taskactivity = new Activity();
        taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
        taskactivity.actorId = user.id;
        taskactivity.type = ActivityType.TASK;
        taskactivity.typeId = task.id;
        taskactivity.remarks = `Past Members: ${taskmembersName.join(
          ', ',
        )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
        await taskactivity.save();
      }

      let members = await User.find({ where: { id: In(membersIds), type: UserType.ORGANIZATION } });
      task.members = members;

      const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
      const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);

      const LeaderIds = body?.taskLeader?.map((item) => item?.id);
      const LeaderName = body?.taskLeader?.map((item) => item?.fullName);

      let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
      let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));

      if (leadersdifference?.length || taskleaderdifference?.length) {
        let taskactivity = new Activity();
        taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
        taskactivity.actorId = user.id;
        taskactivity.type = ActivityType.TASK;
        taskactivity.typeId = task.id;
        taskactivity.remarks = `Past Task Leaders: ${
          taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
        }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
        await taskactivity.save();
      }
      let leaders = await User.find({ where: { id: In(LeaderIds), type: UserType.ORGANIZATION } });
      task.taskLeader = leaders;

      task['userId'] = user.id;
    }

    let taskStatus = new TaskStatus();
    taskStatus.status = task.restore ? task.restore : TaskStatusEnum.TODO;
    taskStatus.restore = task.restore ? task.restore : TaskStatusEnum.TODO;
    taskStatus.task = task;
    taskStatus.user = user;
    await taskStatus.save();
    task['userId'] = user.id;
    task.processInstanceId = null;
    task.approvalProcedures = null;
    task.approvalStatus = null;
    await task.save();

    return { success: true };
  }

  async terminateTask(userId: number, taskId: number, reason: string) {
    let taskLogHour = await LogHour.find({ where: { task: taskId }, relations: ['user'] });
    let user = await User.findOne({ where: { id: userId } });
    if (taskLogHour.length) {
      const lastTaskItem = taskLogHour[taskLogHour.length - 1];
      if (lastTaskItem.status !== 'stopped') {
        let taskLogHourLast = await LogHour.findOne({
          where: { id: lastTaskItem.id },
          relations: ['user'],
        });
        taskLogHourLast.status = TimerStatus.STOPPED;
        taskLogHourLast.completedDate = moment().format('YYYY-MM-DD');
        await taskLogHourLast.save();
      }
    }
    let task = await Task.findOne({
      where: { id: taskId },
      relations: ['client', 'subTasks', 'parentTask', 'clientGroup'],
    });
    this.eventEmitter.emit(Event_Actions.TASK_TERMINATED, {
      userId,
      oldTaskStatus: task.status,
      clientId: task.client?.id,
      task,
    });
    task.restore = task.status;
    task.status = TaskStatusEnum.TERMINATED;
    task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
    task.terminationReason = reason;
    task['userId'] = userId;
    await task.save();

    let taskStatus = new TaskStatus();
    taskStatus.status = task.restore;
    taskStatus.restore = task.restore;
    taskStatus.task = task;
    taskStatus.user = user;
    await taskStatus.save();

    for (let i of task.subTasks) {
      i.status = TaskStatusEnum.TERMINATED;
      i.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      i.save();
    }

    return { success: true };
  }

  async deleteTask(userId: number, taskId: number) {
    let taskLogHour = await LogHour.find({ where: { task: taskId }, relations: ['user'] });
    let user = await User.findOne({ where: { id: userId } });
    if (taskLogHour.length) {
      const lastTaskItem = taskLogHour[taskLogHour.length - 1];
      if (lastTaskItem.status !== 'stopped') {
        let taskLogHourLast = await LogHour.findOne({
          where: { id: lastTaskItem.id },
          relations: ['user'],
        });
        taskLogHourLast.status = TimerStatus.STOPPED;
        taskLogHourLast.completedDate = moment().format('YYYY-MM-DD');
        await taskLogHourLast.save();
      }
    }
    let task = await Task.findOne({
      where: { id: taskId },
      relations: ['client', 'parentTask', 'subTasks', 'clientGroup'],
    });
    this.eventEmitter.emit(Event_Actions.TASK_DELETED, {
      userId,
      oldTaskStatus: task.status,
      clientId: task.client?.id,
      task,
    });

    task.restore = task.status;
    task.status = TaskStatusEnum.DELETED;
    task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
    task['userId'] = user.id;
    await task.save();

    let taskStatus = new TaskStatus();
    taskStatus.status = task.restore;
    taskStatus.restore = task.restore;
    taskStatus.task = task;
    taskStatus.user = user;
    await taskStatus.save();

    for (let i of task.subTasks) {
      i.status = TaskStatusEnum.DELETED;
      i.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      i.save();
    }

    return { success: true };
  }

  async addRemarks(userId: number, taskId: number, data: AddRemkarDto) {
    let task = await Task.findOne({ where: { id: taskId }, relations: ['members', 'taskLeader'] });
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    let assignedMember = task.members.some((member) => member.id === userId);
    let taskLeader = task.taskLeader[0]?.id === userId;

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    if (!assignedMember && !taskLeader && !(user.role.name === 'Admin') && !allTasks) {
      throw new ForbiddenException('You are not allowed to add remarks to this task');
    }

    let activity = new Activity();
    activity.remarkType = data.remarkType;
    activity.remarks = data.remarks;
    activity.action = 'Remark Added';
    activity.actorId = userId;
    activity.type = ActivityType.TASK;
    activity.typeId = task.id;
    await activity.save();

    return activity;
  }

  async getTerminatedTasks(userId: number, query: FindTasksQuery) {
    const { limit, offset, search } = query;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = await createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.name',
        'task.financialYear',
        'task.taskNumber',
        'task.paymentStatus',
        'task.dueDate',
        'task.statusUpdatedAt',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName',
        'members.id',
        'members.fullName',
        'taskLeader.id',
        'taskLeader.fullName',
        'organization.id',
        'budgetedHoursUser.id',
        'budgetedHoursUser.fullName',
      ])
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.organization', 'organization')
      .leftJoin('task.members', 'members')
      .leftJoinAndSelect('task.taskBudgetedHours', 'taskBudgetedHours')
      .leftJoin('taskBudgetedHours.user', 'budgetedHoursUser')
      .leftJoin('task.taskLeader', 'taskLeader')
      .where('organization.id = :orgID', { orgID: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.status = :status', { status: TaskStatusEnum.TERMINATED });

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        terminatedDate: 'task.statusUpdatedAt',
        taskNumber: 'task.taskNumber',
        financialYear: 'task.financialYear',
        priority: 'task.priority',
        dueDate: 'task.dueDate',
        paymentStatus: 'task.status',
        displayName: 'client.displayName',
        name: 'task.name',
        groupName: 'client.displayName',
        statusUpdatedAt: 'task.statusUpdatedAt',
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.statusUpdatedAt', 'DESC');
    }

    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.allTasks === 'sub_task'
        ? tasks.andWhere('task.parentTask is not null')
        : '';
    }

    if (query?.clientId) {
      tasks.andWhere('task.client.id = :client', { client: query?.clientId });
    }

    if (query?.clientGroup) {
      const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoinAndSelect('clientGroup.organization', 'organization')
        .leftJoinAndSelect('clientGroup.clients', 'clients')
        .where('clientGroup.id = :id', { id: query?.clientGroup })
        .andWhere('organization.id = :organization', { organization: user.organization.id })
        .getOne();

      const clientGroupIDs = clientGroup?.clients.map((item) => item.id);

      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.id = :clientGroupId', { clientGroupId: query?.clientGroup });

          if (clientGroupIDs && clientGroupIDs.length > 0) {
            qb.orWhere('client.id IN (:...clientIds)', { clientIds: clientGroupIDs });
          }
        }),
      );
    }

    if (query.search) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('task.taskNumber LIKE :taskNumber', {
            taskNumber: `%${query.search}%`,
          });
          qb.orWhere('task.name LIKE :name', {
            name: `%${query.search}%`,
          });
        }),
      );
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();
    const taskData = result[0];
    for (let task of taskData) {
      task['budgetedHoursData'] = Math.floor(moment.duration(task.budgetedhours).asHours());
      const remainingDuration = moment
        .duration(task.budgetedhours)
        .subtract(Math.floor(moment.duration(task.budgetedhours).asHours()), 'hours');
      const remainingMinutes = Math.floor(remainingDuration.asMinutes());
      task['budgetedMinutesData'] = remainingMinutes;
      for (let i of task.taskBudgetedHours) {
        i['budgetedHoursData'] = Math.floor(moment.duration(i.budgetedHours).asHours());
        const remainingDuration = moment
          .duration(i.budgetedHours)
          .subtract(Math.floor(moment.duration(i.budgetedHours).asHours()), 'hours');
        const remainingMinutes = Math.floor(remainingDuration.asMinutes());
        i['budgetedMinutesData'] = remainingMinutes;
      }
    }

    return {
      count: result[1],
      result: taskData,
    };
  }

  async exportTerminatedTasksReport(userId: number, body: FindTasksQuery) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let tasks = await this.getTerminatedTasks(userId, newQuery);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Terminated Tasks');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client / Client Group', key: 'client' },
      { header: 'Financial Year', key: 'financialYear' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name', key: 'taskName' },
      { header: 'Billing Status', key: 'paymentStatus' },
      { header: 'Due Date', key: 'dueDate' },

      { header: 'Status Updated on', key: 'statusUpdatedAt' },
      { header: 'Task Leader', key: 'taskLeaders' },
      { header: 'Members', key: 'members' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        client: task.client?.displayName || task.clientGroup?.displayName || '',
        financialYear: task.financialYear,
        taskId: task?.taskNumber,
        taskName: task?.name,
        paymentStatus: Boolean(task?.billable) ? 'Billed' : 'Un-Billed',
        dueDate: formatDate(task?.dueDate),
        statusUpdatedAt: task?.statusUpdatedAt
          ? moment(task?.statusUpdatedAt).format('DD-MM-YYYY HH:mm')
          : '',
        taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
        members: task?.members.map((assignee) => assignee.fullName).join(', '),
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (
        column.key === 'taskName' ||
        column.key === 'members' ||
        column.key === 'taskLeaders' ||
        column.key === 'client'
      ) {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportClientTerminatedTaskReport(userId: number, query: FindTasksQuery) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };

    let tasks;
    try {
      tasks = await this.getTerminatedTasks(userId, newQuery);
    } catch (error) {
      throw new BadRequestException('Error fetching data for export');
    }

    if (!tasks || !tasks.result.length) {
      throw new BadRequestException('No data for export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Terminated Tasks');
    const isClientGroup = tasks.result.some((item) => {
      const { clientGroup } = item;
      return item?.clientGroup?.type === 'CLIENT_GROUP' || clientGroup === null;
    });
    const headers = isClientGroup
      ? [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Client Group', key: 'clientGroup' },
          { header: 'Task ID', key: 'taskId' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Terminated Date', key: 'terminateDate' },
          { header: 'Terminated Reason', key: 'terminatedReason' },
          { header: 'Members', key: 'members' },
        ]
      : [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Task ID', key: 'taskId' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Terminated Date', key: 'terminateDate' },
          { header: 'Terminated Reason', key: 'terminatedReason' },
          { header: 'Members', key: 'members' },
        ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    tasks.result.forEach((task) => {
      const rowData = isClientGroup
        ? {
            serialNo: serialCounter++, // Assign and then increment the counter
            taskId: task.taskNumber,
            taskName: task?.name,
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            terminateDate: formatDate(task?.terminatedDate),
            terminatedReason: task?.terminationReason,
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
            clientGroup: task?.clientGroup?.displayName || task?.client.displayName || ' ', // Add client group information
          }
        : {
            serialNo: serialCounter++, // Assign and then increment the counter
            taskId: task.taskNumber,
            taskName: task?.name,
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            terminateDate: formatDate(task?.terminatedDate),
            terminatedReason: task?.terminationReason,
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
          };

      worksheet.addRow(rowData);
    });

    worksheet.columns.forEach((column) => {
      let maxLength = 0;

      column.eachCell({ includeEmpty: true }, (cell) => {
        const cellLength = cell.value ? cell.value.toString().length : 0;
        maxLength = Math.max(maxLength, cellLength);
      });

      column.width = maxLength + 2;

      if (['taskName', 'members', 'taskLeaders', 'clientGroup'].includes(column.key)) {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getCompletedTasks(userId: number, query: FindTasksQuery) {
    const { limit, offset, search } = query;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .where('task.organization.id = :orgID', { orgID: user.organization.id })
      .andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });

    // Sorting Logic

    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.allTasks === 'sub_task'
        ? tasks.andWhere('task.parentTask is not null')
        : '';
    }

    if (query?.clientId) {
      tasks.andWhere('task.client.id = :client', { client: query?.clientId });
    }

    if (query?.clientGroup) {
      const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoinAndSelect('clientGroup.organization', 'organization')
        .leftJoinAndSelect('clientGroup.clients', 'clients')
        .where('clientGroup.id = :id', { id: query?.clientGroup })
        .andWhere('organization.id = :organization', { organization: user.organization.id })
        .getOne();

      const clientGroupIDs = clientGroup?.clients.map((item) => item.id);

      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.id = :clientGroupId', { clientGroupId: query?.clientGroup });

          if (clientGroupIDs && clientGroupIDs.length > 0) {
            qb.orWhere('client.id IN (:...clientIds)', { clientIds: clientGroupIDs });
          }
        }),
      );
    }

    if (search) {
      tasks.andWhere('task.taskNumber LIKE :search OR task.name LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;

    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        completedDate: 'task.statusUpdatedAt',
        paymentStatus: 'task.createdAt',
        groupName: 'client.displayName',
      };

      const column = columnMap[sort.column] || sort.column;

      if (column) {
        tasks.orderBy(column, sort.direction?.toUpperCase() === 'DESC' ? 'DESC' : 'ASC');
      }
    } else {
      tasks.orderBy('task.createdAt', 'DESC');
    }

    let result = await tasks.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async exportClientCompletedTaskReport(userId: number, query: FindTasksQuery) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };

    let tasks;
    try {
      tasks = await this.getCompletedTasks(userId, newQuery);
    } catch (error) {
      throw new BadRequestException('Error fetching data for export');
    }

    if (!tasks || !tasks.result.length) {
      throw new BadRequestException('No data available for export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Completed Tasks');

    // Determine if the data belongs to a client group
    const isClientGroup = tasks.result.some((item) => {
      const { clientGroup } = item;
      return item?.clientGroup?.type === 'CLIENT_GROUP' || clientGroup === null;
    });

    const headers = isClientGroup
      ? [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Client / Client Group', key: 'clientGroup' },
          { header: 'Task ID', key: 'taskId' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Start Date', key: 'startDate' },
          { header: 'Statutory Due Date', key: 'dueDate' },
          { header: 'Priority', key: 'priority' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Members', key: 'members' },
          { header: 'Billing Status', key: 'billingStatus' },
        ]
      : [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Task ID', key: 'taskId' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Start Date', key: 'startDate' },
          { header: 'Statutory Due Date', key: 'dueDate' },
          { header: 'Priority', key: 'priority' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Members', key: 'members' },
          { header: 'Billing Status', key: 'billingStatus' },
        ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    tasks.result.forEach((task) => {
      const rowData = isClientGroup
        ? {
            serialNo: serialCounter++, // Assign and then increment the counter
            clientGroup: task?.clientGroup?.displayName || task?.client.displayName || ' ', // Add client group information
            taskId: task.taskNumber,
            taskName: task?.name,
            startDate: formatDate(task?.createdDate),
            dueDate: formatDate(task?.dueDate),
            priority: getTitle(task?.priority),
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
            billingStatus: task?.paymentStatus,
          }
        : {
            serialNo: serialCounter++, // Assign and then increment the counter
            taskId: task.taskNumber,
            taskName: task?.name,
            startDate: formatDate(task?.createdDate),
            dueDate: formatDate(task?.dueDate),
            priority: getTitle(task?.priority),
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
            billingStatus: task?.paymentStatus,
          };

      // Add the row
      const newRow = worksheet.addRow(rowData);

      // Apply billing status colors
      const billingStatusCell = newRow.getCell('billingStatus');
      const billingStatus = rowData.billingStatus?.toUpperCase();
      if (billingStatus === 'BILLED') {
        billingStatusCell.font = { color: { argb: 'FF00B050' }, bold: true }; // Green for Billed
      } else {
        billingStatusCell.font = { color: { argb: 'FFFF0000' }, bold: true }; // Red for Unbilled
      }
    });

    worksheet.columns.forEach((column) => {
      let maxLength = 0;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const cellLength = cell.value ? cell.value.toString().length : 0;
        maxLength = Math.max(maxLength, cellLength);
      });
      column.width = maxLength + 2;
      if (['taskName', 'members', 'taskLeaders', 'clientGroup'].includes(column.key)) {
        column.width = 50; // Set a fixed width
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' }; // Wrap text and center
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' }; // Center text
      }
    });

    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '64B5F6' } };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getRecurringTasks(query: FindTasksQuery, userId: number) {
    const { limit, offset, search } = query;

    const client = await Client.findOne({
      where: { id: query?.clientId },
      relations: ['organization'],
    });
    const clientGroup = await ClientGroup.findOne({
      where: { id: query?.clientGroup },
      relations: ['organization'],
    });
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage');

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;

    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        frequency: 'task.frequency',
        priority: 'task.priority',
        task_start_date: 'task.taskStartDate',
        due_date: 'task.dueDate',
      };

      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.taskStartDate', 'ASC');
    }

    if (client) {
      tasks.where('task.client.id = :client', { client: client?.id });
    }
    if (clientGroup) {
      const clientGroupQuery = await createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoinAndSelect('clientGroup.organization', 'organization')
        .leftJoinAndSelect('clientGroup.clients', 'clients')
        .where('clientGroup.id = :id', { id: clientGroup?.id })
        .andWhere('organization.id = :organization', { organization: user.organization.id })
        .getOne();

      const clientGroupIDs = clientGroupQuery?.clients.map((item) => item.id);

      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.id = :clientGroupId', { clientGroupId: clientGroup?.id });

          if (clientGroupIDs && clientGroupIDs.length > 0) {
            qb.orWhere('client.id IN (:...clientIds)', { clientIds: clientGroupIDs });
          }
        }),
      );
    }
    tasks
      .andWhere('task.organization.id = :orgID', { orgID: user?.organization?.id })
      .andWhere('task.recurring = true')
      .andWhere('task.recurringStatus = :recurringStatus', {
        recurringStatus: TaskRecurringStatus.PENDING,
      });

    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.allTasks === 'sub_task'
        ? tasks.andWhere('task.parentTask is not null')
        : '';
    }

    if (search) {
      tasks.andWhere('task.taskNumber LIKE :search OR task.name LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }
  async exportgetRecurringTasksReport(userId: number, query: FindTasksQuery) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getRecurringTasks(newQuery, userId);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Upcoming Recurring Task');
    const isClientGroup = tasks.result.some((item) => {
      const { clientGroup } = item;
      return item?.clientGroup?.type === 'CLIENT_GROUP' || clientGroup === null;
    });
    // Define headers
    const headers = isClientGroup
      ? [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Client / Client Group', key: 'clientGroup' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Start Date', key: 'startDate' },
          { header: 'Statutory Due Date', key: 'dueDate' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Priority', key: 'priority' },
          { header: 'Members', key: 'members' },
        ]
      : [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Start Date', key: 'startDate' },
          { header: 'Statutory Due Date', key: 'dueDate' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Priority', key: 'priority' },
          { header: 'Members', key: 'members' },
        ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      // Extract task data including client group info
      const rowData = isClientGroup
        ? {
            serialNo: serialCounter++, // Assign and then increment the counter
            taskName: task?.name,
            startDate: formatDate(task?.taskStartDate) || ' ',
            dueDate: formatDate(task?.dueDate) || ' ',
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            priority: getTitle(task?.priority),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
            clientGroup: task?.clientGroup?.displayName || task?.client.displayName || ' ', // Add client group information
          }
        : {
            serialNo: serialCounter++, // Assign and then increment the counter
            taskName: task?.name,
            startDate: formatDate(task?.taskStartDate) || ' ',
            dueDate: formatDate(task?.dueDate) || ' ',
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            priority: getTitle(task?.priority),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
          };

      const row = worksheet.addRow(rowData);

      // Priority color coding
      const priorityCell = row.getCell('priority');
      switch (rowData.priority?.toLowerCase()) {
        case 'high':
          priorityCell.font = { color: { argb: 'FB0505' }, bold: true };
          break;
        case 'medium':
          priorityCell.font = { color: { argb: 'F17F23' }, bold: true };
          break;
        case 'low':
          priorityCell.font = { color: { argb: '019335' }, bold: true };
          break;
        default:
          priorityCell.font = { color: { argb: '64B5F6' }, bold: true };
          break;
      }

      // Set column widths dynamically based on content length
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    // Apply styles to the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Apply consistent styling to columns
    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapping text to cells
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    // Freeze header row for easy scrolling
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getNonRecurringTasks(query: FindTasksQuery, userId: number) {
    const { limit, offset, search } = query;

    const client = await Client.findOne({
      where: { id: query?.clientId },
      relations: ['organization'],
    });
    const clientGroup = await ClientGroup.findOne({
      where: { id: query?.clientGroup },
      relations: ['organization'],
    });
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage');

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;

    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        priority: 'task.priority',
        task_start_date: 'task.taskStartDate',
        due_date: 'task.dueDate',
      };

      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.taskStartDate', 'ASC');
    }
    if (client) {
      tasks.where('task.client.id = :client', { client: client.id });
    }
    if (clientGroup) {
      const clientGroupQuery = await createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoinAndSelect('clientGroup.organization', 'organization')
        .leftJoinAndSelect('clientGroup.clients', 'clients')
        .where('clientGroup.id = :id', { id: clientGroup?.id })
        .andWhere('organization.id = :organization', { organization: user.organization.id })
        .getOne();

      const clientGroupIDs = clientGroupQuery?.clients.map((item) => item.id);

      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.id = :clientGroupId', { clientGroupId: clientGroup?.id });

          if (clientGroupIDs && clientGroupIDs.length > 0) {
            qb.orWhere('client.id IN (:...clientIds)', { clientIds: clientGroupIDs });
          }
        }),
      );
    }

    tasks
      .andWhere('task.organization.id = :orgID', { orgID: user?.organization?.id })
      .andWhere('task.recurring = false')
      .andWhere('task.recurringStatus = :recurringStatus', {
        recurringStatus: TaskRecurringStatus.PENDING,
      });

    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.allTasks === 'sub_task'
        ? tasks.andWhere('task.parentTask is not null')
        : '';
    }

    if (search) {
      tasks.andWhere('task.taskNumber LIKE :search OR task.name LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async exportgetNonRecurringTasksReport(userId: number, query: FindTasksQuery) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getNonRecurringTasks(newQuery, userId);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');
    const isClientGroup = tasks.result.some((item) => {
      const { clientGroup } = item;
      return item?.clientGroup?.type === 'CLIENT_GROUP' || clientGroup === null;
    });
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Non-Recurring Task');

    // Define the headers with Client Group instead of Billing Status
    const headers = isClientGroup
      ? [
          { header: 'Client / Client Group', key: 'clientGroup' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Start Date', key: 'startDate' },
          { header: 'Statutory Due Date', key: 'dueDate' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Priority', key: 'priority' },
          { header: 'Members', key: 'members' },
        ]
      : [
          { header: 'Task Name', key: 'taskName' },
          { header: 'Start Date', key: 'startDate' },
          { header: 'Statutory Due Date', key: 'dueDate' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Priority', key: 'priority' },
          { header: 'Members', key: 'members' },
        ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      // Extract task data including Client Group
      const rowData = isClientGroup
        ? {
            taskName: task?.name,
            startDate: formatDate(task?.taskStartDate) || ' ',
            dueDate: formatDate(task?.dueDate) || ' ',
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            priority: getTitle(task?.priority),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
            clientGroup: task?.clientGroup?.displayName || task?.client.displayName || ' ', // Add client group information
          }
        : {
            taskName: task?.name,
            startDate: formatDate(task?.taskStartDate) || ' ',
            dueDate: formatDate(task?.dueDate) || ' ',
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            priority: getTitle(task?.priority),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
          };

      const row = worksheet.addRow(rowData);

      // Priority color coding
      const priorityCell = row.getCell('priority');
      switch (rowData.priority?.toLowerCase()) {
        case 'high':
          priorityCell.font = { color: { argb: 'FB0505' }, bold: true };
          break;
        case 'medium':
          priorityCell.font = { color: { argb: 'F17F23' }, bold: true };
          break;
        case 'low':
          priorityCell.font = { color: { argb: '019335' }, bold: true };
          break;
        default:
          priorityCell.font = { color: { argb: '64B5F6' }, bold: true };
          break;
      }

      // Set column widths dynamically based on content length
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Adjust the column widths
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    // Apply styles to the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Apply consistent styling to columns
    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapping text to cells
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    // Freeze header row for easy scrolling
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getUserCompletedTasks(userId: number, query: FindTasksQuery) {
    const { limit, offset, search } = query;

    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = await createQueryBuilder(Task, 'task')
      .select([
        'task',
        'organization.id',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroup.type',
        'members.id',
        'members.fullName',
        'imageStorage',
        'taskLeader.id',
        'taskLeader.fullName',
        'leaderImageStorage',
        'service.id',
        'service.name',
      ])
      .leftJoin('task.organization', 'organization')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.members', 'members')
      .leftJoin('members.imageStorage', 'imageStorage')
      .leftJoin('task.taskLeader', 'taskLeader')
      .leftJoin('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoin('task.service', 'service')
      .where('task.status = :status', { status: TaskStatusEnum.COMPLETED })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (query.fromDate && query.toDate) {
      tasks.andWhere(
        `Date(task.status_updated_at) >= '${moment(query.fromDate).format(
          'YYYY-MM-DD',
        )}' and Date(task.status_updated_at) <= '${moment(query.toDate).format('YYYY-MM-DD')}'`,
      );
    }

    if (query.financialYear) {
      tasks.andWhere('task.financialYear = :financialYear', {
        financialYear: query.financialYear,
      });
    }

    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientname: 'client.displayName',
        financialYear: 'task.financialYear',
        name: 'task.name',
        taskNumber: 'task.taskNumber',
        paymentStatus: 'task.paymentStatus',
        due_date: 'task.dueDate',
        statusUpdatedAt: 'task.statusUpdatedAt',
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.statusUpdatedAt', 'DESC');
    }
    if (query.search) {
      tasks.andWhere(
        `(client.displayName LIKE :clientName OR clientGroup.displayName LIKE :clientName 
      OR task.taskNumber LIKE :taskNumber 
      OR task.name LIKE :name 
      OR service.name LIKE :serviceName)`,
        {
          clientName: `%${query.search}%`,
          taskNumber: `%${query.search}%`,
          name: `%${query.search}%`,
          serviceName: `%${query.search}%`,
        },
      );
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async getDeletedTasks(userId: number, query: FindTasksQuery) {
    const { limit, offset, search } = query;

    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.name',
        'task.financialYear',
        'task.taskNumber',
        'task.paymentStatus',
        'task.dueDate',
        'task.statusUpdatedAt',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName',
        'members.id',
        'members.fullName',
        'taskLeader.id',
        'taskLeader.fullName',
        'organization.id',
        'budgetedHoursUser.id',
        'budgetedHoursUser.fullName',
      ])
      .leftJoin('task.organization', 'organization')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.members', 'members')
      .leftJoin('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('task.taskBudgetedHours', 'taskBudgetedHours')
      .leftJoin('taskBudgetedHours.user', 'budgetedHoursUser')
      .where('task.status = :status', { status: TaskStatusEnum.DELETED })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });

    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.allTasks === 'sub_task'
        ? tasks.andWhere('task.parentTask is not null')
        : '';
    } else {
      tasks.orderBy('task.statusUpdatedAt', 'DESC');
    }
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        deletedDate: 'task.statusUpdatedAt',
        taskNumber: 'task.taskNumber',
        financialYear: 'task.financialYear',
        priority: 'task.priority',
        dueDate: 'task.dueDate',
        paymentStatus: 'task.status',
        displayName: 'client.displayName',
        name: 'task.name',
        groupName: 'client.displayName',
        statusUpdatedAt: 'task.statusUpdatedAt',
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.statusUpdatedAt', 'DESC');
    }
    if (query?.client) {
      tasks.andWhere('task.client.id = :client', { client: query?.client });
    }

    if (query?.clientGroup) {
      const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoinAndSelect('clientGroup.organization', 'organization')
        .leftJoinAndSelect('clientGroup.clients', 'clients')
        .where('clientGroup.id = :id', { id: query?.clientGroup })
        .andWhere('organization.id = :organization', { organization: user.organization.id })
        .getOne();

      const clientGroupIDs = clientGroup?.clients.map((item) => item.id);

      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.id = :clientGroupId', { clientGroupId: query?.clientGroup });

          if (clientGroupIDs && clientGroupIDs.length > 0) {
            qb.orWhere('client.id IN (:...clientIds)', { clientIds: clientGroupIDs });
          }
        }),
      );
    }

    if (query.search) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('task.taskNumber LIKE :search', { search: `%${query.search}%` })
            .orWhere('task.name LIKE :search', { search: `%${query.search}%` })
            .orWhere('client.displayName LIKE :search', { search: `%${query.search}%` })
            .orWhere('clientGroup.displayName LIKE :search', { search: `%${query.search}%` });
        }),
      );
    }

    if (query?.client) {
      tasks.andWhere('task.client.id = :clientId', { clientId: query.client });
    }
    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();
    const taskData = result[0];

    for (let task of taskData) {
      task['budgetedHoursData'] = Math.floor(moment.duration(task.budgetedhours).asHours());
      const remainingDuration = moment
        .duration(task.budgetedhours)
        .subtract(Math.floor(moment.duration(task.budgetedhours).asHours()), 'hours');
      const remainingMinutes = Math.floor(remainingDuration.asMinutes());
      task['budgetedMinutesData'] = remainingMinutes;
      for (let i of task.taskBudgetedHours) {
        i['budgetedHoursData'] = Math.floor(moment.duration(i.budgetedHours).asHours());
        const remainingDuration = moment
          .duration(i.budgetedHours)
          .subtract(Math.floor(moment.duration(i.budgetedHours).asHours()), 'hours');
        const remainingMinutes = Math.floor(remainingDuration.asMinutes());
        i['budgetedMinutesData'] = remainingMinutes;
      }
    }

    return {
      count: result[1],
      result: taskData,
    };
  }

  async exportClientDeletedTasksReport(userId: number, query: FindTasksQuery) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getDeletedTasks(userId, newQuery);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');
    const isClientGroup = tasks.result.some((item) => {
      const { clientGroup } = item;
      return item?.clientGroup?.type === 'CLIENT_GROUP' || clientGroup === null;
    });

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Deleted Tasks');
    const headers = isClientGroup
      ? [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Client / Client Group', key: 'clientGroup' }, // New column for client group
          { header: 'Task ID', key: 'taskId' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Deleted Date', key: 'deletedDate' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Members', key: 'members' },
          { header: 'Billing Status', key: 'billingStatus' },
        ]
      : [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Task ID', key: 'taskId' },
          { header: 'Task Name', key: 'taskName' },
          { header: 'Deleted Date', key: 'deletedDate' },
          { header: 'Task Leader', key: 'taskLeaders' },
          { header: 'Members', key: 'members' },
          { header: 'Billing Status', key: 'billingStatus' },
        ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    tasks.result.forEach((task) => {
      const rowData = isClientGroup
        ? {
            serialNo: serialCounter++, // Assign and then increment the counter
            taskId: task.taskNumber,
            taskName: task?.name,
            deletedDate: formatDate(task?.createdDate),
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
            billingStatus: task?.paymentStatus,
            clientGroup: task?.clientGroup?.displayName || task?.client.displayName || ' ', // Add client group information
          }
        : {
            serialNo: serialCounter++, // Assign and then increment the counter
            taskId: task.taskNumber,
            taskName: task?.name,
            deletedDate: formatDate(task?.createdDate),
            taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
            billingStatus: task?.paymentStatus,
          };

      const row = worksheet.addRow(rowData);

      // Format the Billing Status cell based on its value
      const billingStatusCell = row.getCell('billingStatus');
      if (rowData.billingStatus?.toUpperCase() === 'BILLED') {
        billingStatusCell.font = { color: { argb: 'FF00B050' }, bold: true };
      } else {
        billingStatusCell.font = { color: { argb: 'FFFF0000' }, bold: true };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (
        column.key === 'taskName' ||
        column.key === 'members' ||
        column.key === 'taskLeaders' ||
        column.key === 'clientGroup'
      ) {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getTerminatedTasksList(userId: number, query: FindTasksQuery) {
    const { limit, offset } = query;

    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.members', 'members')
      .where('task.status = :status', { status: TaskStatusEnum.TERMINATED })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });

    if (query?.client) {
      tasks.andWhere('task.client.id = :clientId', { clientId: query.client });
    }
    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async getAssignedTasks(userId: number, query: any) {
    let tasks = createQueryBuilder(Task, 'task')
      .select(['task.name'])
      .leftJoin('task.category', 'category')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.members', 'members')
      .where('members.id = :userId', { userId });

    if (query.client) {
      tasks = tasks.andWhere('client.id = :clientId', { clientId: query.client });
    }

    let result = await tasks.getMany();
    return result;
  }

  async updateTaskMemebers(id: number) {
    const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
      .leftJoinAndSelect('recurringProfile.tasks', 'task')
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('task.labels', 'labels')
      .leftJoinAndSelect('task.members', 'member')
      .leftJoinAndSelect('task.taskBudgetedHours', 'taskBudgetedHours')
      .leftJoinAndSelect('taskBudgetedHours.user', 'user')
      .where('recurringProfile.id = :id', { id })
      .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
      .orderBy({ 'task.taskStartDate': 'ASC' })
      .getOne();

    const taskData = recurringProfile.tasks[0];
    taskData['budgetedHoursData'] = Math.floor(moment.duration(taskData.budgetedhours).asHours());
    const remainingDuration = moment
      .duration(taskData.budgetedhours)
      .subtract(Math.floor(moment.duration(taskData.budgetedhours).asHours()), 'hours');
    const remainingMinutes = Math.floor(remainingDuration.asMinutes());
    taskData['budgetedMinutesData'] = remainingMinutes;
    const taskBudgetedHours = {};
    for (let i of taskData.taskBudgetedHours) {
      i['budgetedHoursData'] = Math.floor(moment.duration(i.budgetedHours).asHours());
      const remainingDuration = moment
        .duration(i.budgetedHours)
        .subtract(Math.floor(moment.duration(i.budgetedHours).asHours()), 'hours');
      const remainingMinutes = Math.floor(remainingDuration.asMinutes());
      i['budgetedMinutesData'] = remainingMinutes;
      taskBudgetedHours[i.user.id] = i;
    }
    taskData['taskBudgetedHoursData'] = taskBudgetedHours;
    return taskData;
  }

  async importTasks(userId, body) {
    for (const item of body) {
      let client = await Client.findOne({ where: { id: item.client } });
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let taskLeader = await User.find({
        where: { id: In(item.taskLeader), type: UserType.ORGANIZATION },
      });
      let members = await User.find({ where: { id: In(item.users), type: UserType.ORGANIZATION } });
      let category = await Category.findOne({ where: { id: item.category } });
      let sCategory = await Category.findOne({ where: { id: item.subCategory } });
      let service: Service;
      let appHier;

      if (item.serviceType === ServiceType.STANDARD) {
        let result = await createQueryBuilder(Service, 'service')
          .leftJoinAndSelect('service.category', 'category')
          .leftJoinAndSelect('service.subCategory', 'subCategory')
          .leftJoinAndSelect('service.checklists', 'checklists')
          .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
          .leftJoinAndSelect('service.subTasks', 'subTasks')
          .leftJoinAndSelect('service.milestones', 'milestones')
          .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
          .where('service.id = :id', { id: item.service })
          .getOne();
        service = result;
        if (item.approvalHierarchy) {
          appHier = item.approvalHierarchy;
        }
      }

      const args = {
        userId: userId,
        data: item,
        user: user,
        taskLeader: taskLeader,
        members: members,
        service: service,
        appHier: appHier,
        category: category,
        subCategory: sCategory,
        labels: null,
      };

      try {
        if (item.taskType === TaskType.NON_RECURRING) {
          await this.createNonRecurringTask({ ...args, client, clientGroup: null });
        }
      } catch (err) {
        console.log(err);
        throw new InternalServerErrorException('Internal server error');
      }
    }
  }

  async getBulkTasks(userId: number, body: getBulkTasks) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;
    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoin('task.service', 'service')
      .leftJoin('task.category', 'category')
      .leftJoin('task.subCategory', 'subCategory')
      .leftJoin('task.user', 'user');

    tasks
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('approvalLevels.user', 'approvalLevelsUsers');

    tasks.where('task.organization.id = :organization', { organization: user.organization.id });

    if (assigned) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('approvalLevelsUsers.id = :userId AND task.status = :statusApproval', {
            userId,
            statusApproval: TaskStatusEnum.UNDER_REVIEW,
          })
            .orWhere((qb) => {
              const subQuery = qb
                .subQuery()
                .select('task_id')
                .from('task_members_user', 'taskMembers')
                .where('taskMembers.user_id= :userId')
                .getQuery();
              qb.where(`task.id IN ${subQuery}`);
            })
            .orWhere('taskLeader.id = :userId', { userId });
        }),
      );
    }

    tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    });

    tasks
      .andWhere(
        new Brackets((qb) =>
          qb
            .where('task.status IN (:...statuses)', {
              statuses: [
                TaskStatusEnum.TODO,
                TaskStatusEnum.IN_PROGRESS,
                TaskStatusEnum.ON_HOLD,
                TaskStatusEnum.UNDER_REVIEW,
                TaskStatusEnum.TERMINATED,
                TaskStatusEnum.DELETED,
                TaskStatusEnum.COMPLETED,
              ],
            })
            .orWhere('task.status = :completedStatus', {
              completedStatus: TaskStatusEnum.COMPLETED,
            })
            .andWhere('task.statusUpdatedAt >= :todayDate', {
              todayDate: moment().subtract(15, 'days').toDate(),
            }),
        ),
      )

      // .addOrderBy('task.createdDate', 'DESC')
      .addOrderBy('task.id', 'DESC');

    if (body.category) {
      tasks.andWhere('category.id =:category', { category: body.category });
    }

    if (body.subCategory) {
      tasks.andWhere('subCategory.id =:subCategory', { subCategory: body.subCategory });
    }

    if (body.members) {
      // tasks.andWhere('members.id  =:members', { members: body.members });
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where(
            'EXISTS (SELECT 1 FROM task_members_user tm WHERE tm.task_id = task.id AND tm.user_id =:memberIds)',
            { memberIds: body.members },
          );
        }),
      );
    }

    if (body.taskLeader) {
      // const taskLeadermemberIds = body.taskLeader.map((member: any) => member?.id || member);
      if (body.taskLeader === 'NA') {
        tasks.andWhere('taskLeader.id is null');
      } else {
        tasks.andWhere(
          new Brackets((qb) => {
            qb.where(
              'EXISTS (SELECT 1 FROM task_task_leader_user tm WHERE tm.task_id = task.id AND tm.user_id =:memberIds)',
              { memberIds: body.taskLeader },
            );
          }),
        );
      }
    }

    if (body.service) {
      tasks.andWhere('task.service.id =:serviceId', { serviceId: body.service });
    }

    //START DATE
    if (body.fromDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', { fromDate: body.fromDate });
    }
    if (body.toDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: body.toDate,
      });
    }

    //DUE DATE
    if (body.dueFromDate) {
      tasks.andWhere('task.dueDate >= :fromDate', { fromDate: body.dueFromDate });
    }
    if (body.dueToDate) {
      tasks.andWhere('task.dueDate <= :toDate', {
        toDate: body.dueToDate,
      });
    }

    //COMPLETION DATE
    if (body.completionFromDate) {
      tasks.andWhere('task.expectedCompletionDate >= :fromDate', {
        fromDate: body.completionFromDate,
      });
    }
    if (body.completionToDate) {
      tasks.andWhere('task.expectedCompletionDate <= :toDate', {
        toDate: body.completionToDate,
      });
    }

    if (body.priority?.length) {
      tasks.andWhere('task.priority IN (:...priority)', { priority: body.priority });
    }

    if (body.billingType !== BillableType.ALL) {
      if (body.billingType === BillableType.BILLABLE) {
        tasks.andWhere('task.billable is true');
      } else {
        tasks.andWhere('task.billable is false');
      }
    }

    if (body.taskServiceType !== 'ALL') {
      if (body.taskServiceType === 'STANDARD') {
        tasks.andWhere('task.service IS NOT NULL');
      } else {
        tasks.andWhere('task.service IS NULL');
      }
    }

    if (body.status?.length) {
      tasks.andWhere('task.status IN (:...status)', { status: body.status });
    }

    if (!body?.clientType && body?.client) {
      const clientId: any = body.client;
      tasks.andWhere('client.id = :client', { client: clientId });
    }

    if (body?.clientType) {
      const clientId: any = body.client;
      tasks.andWhere('clientGroup.id = :client', { client: clientId });
    }

    if (body?.financialYear) {
      tasks.andWhere('task.financialYear=:financialYear', { financialYear: body.financialYear });
    }

    if (body?.createdBy === 'Automatically') {
      tasks.andWhere('task.recurring is true');
    }

    if (body.createdBy && body?.createdBy !== 'Automatically') {
      tasks.andWhere('user.id  =:createdBy', {
        createdBy: body.createdBy,
      });
    }

    if (body.createdFromDate || body.createdToDate) {
      if (body.createdFromDate === body.createdToDate) {
        tasks.andWhere(`Date(task.createdAt) = '${body.createdFromDate}'`);
      } else {
        tasks.andWhere(
          `Date(task.createdAt) between '${body.createdFromDate}' and '${body.createdToDate}'`,
        );
      }
    }

    if (body?.offset) {
      tasks.skip(body?.offset);
    }

    if (body?.limit) {
      tasks.take(body?.limit);
    }

    let data = await tasks.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async getBulkRecurringProfile(userId: number, body: getBulkTasks) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let recurringProfile = createQueryBuilder(RecurringProfile, 'recurringProfile')
      .leftJoinAndSelect('recurringProfile.tasks', 'tasks')
      .leftJoinAndSelect('recurringProfile.client', 'client')
      .leftJoinAndSelect('recurringProfile.clientGroup', 'clientGroup')
      .leftJoinAndSelect('tasks.category', 'category')
      .leftJoinAndSelect('tasks.subCategory', 'subCategory')
      .leftJoinAndSelect('tasks.service', 'service')
      .leftJoinAndSelect('tasks.labels', 'labels')
      .leftJoinAndSelect('tasks.members', 'members')
      .leftJoinAndSelect('tasks.taskLeader', 'taskLeader')
      .leftJoinAndSelect('tasks.organization', 'organization')
      .leftJoinAndSelect('tasks.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('approvalLevels.user', 'user');

    recurringProfile
      .where('tasks.organization.id = :organization', { organization: user.organization.id })
      .andWhere('(tasks.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.PENDING,
      });

    if (assigned) {
      recurringProfile.andWhere(
        new Brackets((qb) => {
          qb.where('user.id = :userId AND task.status = :statusApproval', {
            userId,
            statusApproval: TaskStatusEnum.UNDER_REVIEW,
          })
            .orWhere((qb) => {
              const subQuery = qb
                .subQuery()
                .select('task_id')
                .from('task_members_user', 'taskMembers')
                .where('taskMembers.user_id= :userId')
                .getQuery();
              qb.where(`tasks.id IN ${subQuery}`);
            })
            .orWhere('taskLeader.id = :userId', { userId });
        }),
      );
    }

    if (body?.frequency) {
      recurringProfile.andWhere('recurringProfile.frequency = :frequency', {
        frequency: body?.frequency,
      });
    }

    if (body.category) {
      recurringProfile.andWhere('category.id =:category', { category: body.category });
    }

    if (body.billingType !== BillableType.ALL) {
      if (body.billingType === BillableType.BILLABLE) {
        recurringProfile.andWhere('tasks.billable is true');
      } else {
        recurringProfile.andWhere('tasks.billable is false');
      }
    }

    if (body.taskServiceType !== 'ALL') {
      if (body.taskServiceType === 'STANDARD') {
        recurringProfile.andWhere('tasks.service IS NOT NULL');
      } else {
        recurringProfile.andWhere('tasks.service IS NULL');
      }
    }

    if (body.subCategory) {
      recurringProfile.andWhere('subCategory.id =:subCategory', { subCategory: body.subCategory });
    }

    if (body.members) {
      recurringProfile.andWhere(
        new Brackets((qb) => {
          qb.where(
            'EXISTS (SELECT 1 FROM task_members_user tm WHERE tm.task_id = tasks.id AND tm.user_id =:memberIds)',
            { memberIds: body.members },
          );
        }),
      );
    }

    if (body.taskLeader && body.taskLeader !== 'NA') {
      recurringProfile.andWhere(
        new Brackets((qb) => {
          qb.where(
            'EXISTS (SELECT 1 FROM task_task_leader_user tm WHERE tm.task_id = tasks.id AND tm.user_id =:memberIds)',
            { memberIds: body.taskLeader },
          );
        }),
      );
    }
    if (body.approvalUser && body.approvalUser !== 'NA') {
      recurringProfile.andWhere(
        new Brackets((qb) => {
          qb.where(
            `EXISTS (
          SELECT 1
          FROM task t
          INNER JOIN approval_procedures ap ON ap.id = t.approval_procedures_id
          INNER JOIN org_approvals oa ON oa.id = ap.approval_id
          INNER JOIN org_approval_level oal ON oal.approval_hierarchy_id = oa.id
          WHERE t.recurring_profile_id = recurringProfile.id
          AND oal.user_id = :approvalUserId
          AND t.recurring_status=:recurringStatus
        )`,
            { approvalUserId: body.approvalUser, recurringStatus: TaskRecurringStatus.PENDING },
          );
        }),
      );
    }

    if (body.service) {
      recurringProfile.andWhere('tasks.service.id =:serviceId', { serviceId: body.service });
    }

    if (body.priority?.length) {
      recurringProfile.andWhere('tasks.priority IN (:...priority)', { priority: body.priority });
    }

    if (!body?.clientType && body?.client) {
      const clientId: any = body?.client;
      recurringProfile.andWhere('client.id = :client', { client: clientId });
    }

    if (body?.clientType) {
      const clientId: any = body.client;
      recurringProfile.andWhere('clientGroup.id = :client', { client: clientId });
    }

    if (body?.offset) {
      recurringProfile.skip(body?.offset);
    }

    if (body?.limit) {
      recurringProfile.take(body?.limit);
    }

    let data = await recurringProfile.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async getBulkNonRecurring(userId: number, body: getBulkTasks) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let tasks = createQueryBuilder(Task, 'tasks')
      .leftJoinAndSelect('tasks.client', 'client')
      .leftJoinAndSelect('tasks.clientGroup', 'clientGroup')
      .leftJoinAndSelect('tasks.category', 'category')
      .leftJoinAndSelect('tasks.subCategory', 'subCategory')
      .leftJoinAndSelect('tasks.service', 'service')
      .leftJoinAndSelect('tasks.labels', 'labels')
      .leftJoinAndSelect('tasks.members', 'members')
      .leftJoinAndSelect('tasks.taskLeader', 'taskLeader')
      .leftJoinAndSelect('tasks.organization', 'organization')
      .leftJoinAndSelect('tasks.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('approvalLevels.user', 'user');

    tasks
      .where('tasks.organization.id = :organization', { organization: user.organization.id })
      .andWhere('(tasks.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.PENDING,
      })
      .andWhere('(tasks.recurring is not true)');

    if (assigned) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('user.id = :userId AND task.status = :statusApproval', {
            userId,
            statusApproval: TaskStatusEnum.UNDER_REVIEW,
          })
            .orWhere((qb) => {
              const subQuery = qb
                .subQuery()
                .select('taskMembers.task_id')
                .from('task_members_user', 'taskMembers')
                .where('taskMembers.user_id = :userId')
                .getQuery();

              // 👇 wrap with parentheses
              qb.where(`tasks.id IN (${subQuery})`);
            })
            .orWhere('taskLeader.id = :userId', { userId });
        }),
      );
    }

    if (body?.frequency) {
      tasks.andWhere('tasks.frequency = :frequency', { frequency: body?.frequency });
    }

    if (body.category) {
      tasks.andWhere('category.id =:category', { category: body.category });
    }

    if (body.billingType !== BillableType.ALL) {
      if (body.billingType === BillableType.BILLABLE) {
        tasks.andWhere('tasks.billable is true');
      } else {
        tasks.andWhere('tasks.billable is false');
      }
    }

    if (body.taskServiceType !== 'ALL') {
      if (body.taskServiceType === 'STANDARD') {
        tasks.andWhere('tasks.service IS NOT NULL');
      } else {
        tasks.andWhere('tasks.service IS NULL');
      }
    }

    if (body.subCategory) {
      tasks.andWhere('subCategory.id =:subCategory', { subCategory: body.subCategory });
    }

    if (body.members) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where(
            'EXISTS (SELECT 1 FROM task_members_user tm WHERE tm.task_id = tasks.id AND tm.user_id =:memberIds)',
            { memberIds: body.members },
          );
        }),
      );
    }

    if (body.taskLeader && body.taskLeader !== 'NA') {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where(
            'EXISTS (SELECT 1 FROM task_task_leader_user tm WHERE tm.task_id = tasks.id AND tm.user_id =:memberIds)',
            { memberIds: body.taskLeader },
          );
        }),
      );
    }

    if (body.approvalUser && body.approvalUser !== 'NA') {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('user.id = :userId', {
            userId: body.approvalUser,
          });
        }),
      );
    }

    if (body.service) {
      tasks.andWhere('tasks.service.id =:serviceId', { serviceId: body.service });
    }

    if (body.priority?.length) {
      tasks.andWhere('tasks.priority IN (:...priority)', { priority: body.priority });
    }

    if (!body?.clientType && body?.client) {
      const clientId: any = body?.client;
      tasks.andWhere('client.id = :client', { client: clientId });
    }

    if (body?.clientType) {
      const clientId: any = body.client;
      tasks.andWhere('clientGroup.id = :client', { client: clientId });
    }

    if (body?.offset) {
      tasks.skip(body?.offset);
    }

    if (body?.limit) {
      tasks.take(body?.limit);
    }

    let data = await tasks.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async bulkUpdateTaskFields(ids, taskField, value, userId) {
    try {
      const tasks = await Task.find({
        where: { id: In(ids) },
      });
      let user = await User.findOne({
        where: {
          id: userId,
        },
      });

      const validTaskIds = [];
      const errorsList = [];
      if (taskField === 'start_date' || taskField === 'expected_completion_date') {
        for (const task of tasks) {
          if (task.status === TaskStatusEnum.DELETED || task.status === TaskStatusEnum.TERMINATED) {
            const errorDes = `Can't Change Due to Task ${getTitle(task.status)}`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: errorDes,
            });
            continue;
          }
          if (taskField === 'start_date') {
            if (task.expectedCompletionDate && value && !(task.expectedCompletionDate >= value)) {
              const errorDes = 'Start Date should be before the Expected Completion Date';
              errorsList.push({
                taskId: task.taskNumber,
                taskName: task.name,
                error: errorDes,
              });
              continue;
            } else {
              validTaskIds.push(task.id);
              if (task.taskStartDate !== moment(value).format('YYYY-MM-DD')) {
                let activity = new Activity();
                activity.action = Event_Actions.START_DATE_UPDATED;
                activity.actorId = userId;
                activity.type = ActivityType.TASK;
                activity.typeId = task?.id;
                activity.remarks = `"${task?.taskNumber}" Task Start Date Changed from ${moment(
                  value,
                ).format('DD-MM-YYYY')} to ${moment(task.taskStartDate).format('DD-MM-YYYY')} by ${
                  user.fullName
                }`;
                activity.remarks = `"${task?.taskNumber}" Task Start Date Changed from ${moment(
                  value,
                ).format('DD-MM-YYYY')} to ${moment(task.taskStartDate).format('DD-MM-YYYY')} by ${
                  user.fullName
                }`;
                await activity.save();
              }
              continue;
            }
          } else if (taskField === 'expected_completion_date') {
            if (value && task.taskStartDate && !(value >= task.taskStartDate)) {
              const errorDes = 'Expected Completion Date should be after the Start Date';
              errorsList.push({
                taskId: task.taskNumber,
                taskName: task.name,
                error: errorDes,
              });
              continue;
            } else if (value && task.taskStartDate && value >= task.taskStartDate) {
              validTaskIds.push(task.id);
              if (
                moment(task.expectedCompletionDate).format('YYYY-MM-DD') !==
                moment(value).format('YYYY-MM-DD')
              ) {
                let activity = new Activity();
                activity.action = Event_Actions.EXPECTED_COMPLETEION_DATE_CHANGED;
                activity.actorId = userId;
                activity.type = ActivityType.TASK;
                activity.typeId = task?.id;
                activity.remarks = `"${
                  task?.taskNumber
                }" Task Expected Completion Date Changed from ${moment(value).format(
                  'DD-MM-YYYY',
                )} to ${moment(task.expectedCompletionDate).format('DD-MM-YYYY')} by ${
                  user.fullName
                }`;
                await activity.save();
              }
              continue;
            }
          }
        }
      } else {
        for (const task of tasks) {
          if (task.status === TaskStatusEnum.DELETED || task.status === TaskStatusEnum.TERMINATED) {
            const errorDes = `Can't Change Due to Task ${getTitle(task.status)}`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: errorDes,
            });
            continue;
          }
          validTaskIds.push(task.id);
          if (task.dueDate !== moment(value).format('YYYY-MM-DD')) {
            let activity = new Activity();
            activity.action = Event_Actions.STATUTORY_DUE_DATE_CHANGED;
            activity.actorId = userId;
            activity.type = ActivityType.TASK;
            activity.typeId = task?.id;
            activity.remarks = `"${task?.taskNumber}" Task Statutory Due Date Changed from ${moment(
              value,
            ).format('DD-MM-YYYY')} to ${moment(task.dueDate).format('DD-MM-YYYY')} by ${
              user.fullName
            }`;
            await activity.save();
          }
          if (task.priority !== value) {
            let activity = new Activity();
            activity.action = Event_Actions.PRIORITY_UPDATED;
            activity.actorId = userId;
            activity.type = ActivityType.TASK;
            activity.typeId = task?.id;
            activity.remarks = `"${task?.taskNumber}" Task Priority Changed from "${getTitle(
              task.priority,
            )}" to "${getTitle(task.priority)}" by ${user.fullName}`;
            await activity.save();
          }
          if ((task.billable ? 'BILLABLE' : 'NON_BILLABLE') !== value) {
            let activity = new Activity();
            activity.action = Event_Actions.BILLING_TYPE_CHANGED;
            activity.actorId = userId;
            activity.type = ActivityType.TASK;
            activity.typeId = task?.id;
            activity.remarks = `"${task?.taskNumber}" Task Billing Type Changed from ${
              task?.billable ? 'Billable' : 'Non-Billable'
            } to ${value === 'BILLABLE' ? 'Billable' : 'Non-Billable'} by ${user.fullName}`;
            await activity.save();
          }
        }
      }

      if (validTaskIds.length > 0) {
        let updatePayload;
        if (taskField === 'priority') {
          updatePayload = { priority: value };
        } else if (taskField === 'start_date') {
          updatePayload = { taskStartDate: value };
        } else if (taskField === 'due_date') {
          updatePayload = { dueDate: value };
        } else if (taskField === 'billing_type') {
          updatePayload = { billable: value === 'BILLABLE' ? true : false };
        } else if (taskField === 'expected_completion_date') {
          updatePayload = { expectedCompletionDate: value };
        }
        await createQueryBuilder()
          .update(Task)
          .set(updatePayload)
          .where('id IN (:...ids)', { ids: validTaskIds })
          .execute();
      } else {
        // console.log("No valid tasks to update");
      }
      return { errorsList, validTaskIds };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateTaskUsers(ids, taskField, arrayValue, userId) {
    function handleText(text) {
      return text
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    try {
      const tasks = await Task.find({
        where: { id: In(ids) },
        relations: ['members', 'taskBudgetedHours'],
      });

      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const validTaskIds = [];
      const errorsList = [];
      for (const task of tasks) {
        task['bulkUpdate'] = true;
        if (task.status === TaskStatusEnum.DELETED || task.status === TaskStatusEnum.TERMINATED) {
          const errorDes = `Can't Change Due to Task ${getTitle(task.status)}`;
          errorsList.push({
            taskId: task.taskNumber,
            taskName: task.name,
            error: errorDes,
          });
          continue;
        }
        if (['add_user', 'update_user', 'remove_user'].includes(taskField)) {
          if (task?.budgetedhours > 0 && taskField !== 'remove_user') {
            const errorDes = `Cannot ${handleText(taskField)} because this task has Budgeted Hours`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: errorDes,
            });
            continue;
          } else if (taskField === 'remove_user') {
            if (task.members.length === 1) {
              const errorDes = 'Task must contain atleast one member';
              errorsList.push({
                taskId: task.taskNumber,
                taskName: task.name,
                error: errorDes,
              });
              continue;
            } else if (task.members.length > 1) {
              const newMembers = await User.find({
                where: { id: In(arrayValue), type: UserType.ORGANIZATION },
              });
              const existingIds = task.members.map((item) => item.id);
              const newIds = newMembers.map((item) => item.id);
              const nonExistingIds = newIds.filter((id) => !existingIds.includes(id));
              if (nonExistingIds?.length > 0) {
                const errorDes = 'Cannot remove a member who is not assigned in the task';
                errorsList.push({
                  taskId: task.taskNumber,
                  taskName: task.name,
                  error: errorDes,
                });
                continue;
              } else if (nonExistingIds?.length === 0) {
                const taskMemebersIds = task.members.map((item) => item?.id);
                const taskmembersName = task.members.map((item) => item.fullName);
                task.members = task.members.filter((member) => !arrayValue.includes(member.id));
                if (task.members.length === 0) {
                  const errorDes = 'Task must contain atleast one member';
                  errorsList.push({
                    clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                    taskId: task.taskNumber,
                    taskName: task.name,
                    error: errorDes,
                  });
                  continue;
                }
                if (task?.budgetedhours != 0) {
                  const taskBudgetedHours = await BudgetedHours.find({
                    where: { task: task },
                    relations: ['user'],
                  });
                  for (let i of taskBudgetedHours) {
                    if (arrayValue.includes(i?.user?.id)) {
                      const previousBudgetedHours = i?.budgetedHours;
                      i.status = BudgetedHourStatus.INACTIVE;
                      await i.save();
                      task.budgetedhours = task.budgetedhours - previousBudgetedHours;
                    }
                  }
                }
                validTaskIds.push(task.id);
                const membersIds = task.members.map((item) => item?.id);
                const membersName = task.members.map((item) => item.fullName);
                let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
                let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));
                if (membersdifference.length || taskmembersdifference.length) {
                  let taskactivity = new Activity();
                  taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
                  taskactivity.actorId = user.id;
                  taskactivity.type = ActivityType.TASK;
                  taskactivity.typeId = task.id;
                  taskactivity.remarks = `Past Members: ${taskmembersName.join(
                    ', ',
                  )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
                  await taskactivity.save();
                }
                continue;
              }
            }
          } else if (taskField === 'update_user') {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const taskMemebersIds = task.members.map((item) => item?.id);
            const taskmembersName = task.members.map((item) => item.fullName);
            task.members = newMembers;
            const membersIds = task.members.map((item) => item?.id);
            const membersName = task.members.map((item) => item.fullName);
            let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
            let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));
            if (membersdifference.length || taskmembersdifference.length) {
              let taskactivity = new Activity();
              taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
              taskactivity.actorId = user.id;
              taskactivity.type = ActivityType.TASK;
              taskactivity.typeId = task.id;
              taskactivity.remarks = `Past Members: ${taskmembersName.join(
                ', ',
              )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
              await taskactivity.save();
            }
            validTaskIds.push(task.id);
            continue;
          } else if (taskField === 'add_user') {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const taskMemebersIds = task.members.map((item) => item?.id);
            const taskmembersName = task.members.map((item) => item.fullName);
            task.members = [...task.members, ...newMembers];
            const membersIds = task.members.map((item) => item?.id);
            const membersName = task.members.map((item) => item.fullName);
            let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
            let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));
            if (membersdifference.length || taskmembersdifference.length) {
              let taskactivity = new Activity();
              taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
              taskactivity.actorId = user.id;
              taskactivity.type = ActivityType.TASK;
              taskactivity.typeId = task.id;
              taskactivity.remarks = `Past Members: ${taskmembersName.join(
                ', ',
              )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
              await taskactivity.save();
            }
            validTaskIds.push(task.id);
            continue;
          }
        }
      }
      const validTasks = tasks.filter((task) => validTaskIds.includes(task.id));
      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateTaskLeaders(ids, taskField, arrayValue, userId) {
    try {
      const tasks = await Task.find({
        where: { id: In(ids) },
        relations: ['taskLeader'],
      });

      const validTaskIds = [];
      const errorsList = [];

      for (const task of tasks) {
        task['bulkUpdate'] = true;
        if (task.status === TaskStatusEnum.DELETED || task.status === TaskStatusEnum.TERMINATED) {
          const errorDes = `Can't Change Due to Task ${getTitle(task.status)}`;
          errorsList.push({
            taskId: task.taskNumber,
            taskName: task.name,
            error: errorDes,
          });
          continue;
        }
        if (taskField === 'remove_task_leader') {
          if (task?.taskLeader?.length > 0) {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const existingIds = task?.taskLeader?.map((item) => item.id);
            const newIds = newMembers.map((item) => item.id);
            const nonExistingIds = newIds.filter((id) => !existingIds.includes(id));
            if (nonExistingIds?.length > 0) {
              const errorDes = 'Cannot remove a leader who is not assigned in the task';
              errorsList.push({
                taskId: task.taskNumber,
                taskName: task.name,
                error: errorDes,
              });
              continue;
            } else if (nonExistingIds?.length === 0) {
              const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
              const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
              task.taskLeader = task.taskLeader.filter((member) => !arrayValue.includes(member.id));
              const LeaderIds = task?.taskLeader?.map((item) => item?.id);
              const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
              let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
              let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
              if (leadersdifference?.length || taskleaderdifference?.length) {
                let taskactivity = new Activity();
                taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
                taskactivity.actorId = userId;
                taskactivity.type = ActivityType.TASK;
                taskactivity.typeId = task.id;
                taskactivity.remarks = `Past Task Leaders: ${
                  taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
                }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
                await taskactivity.save();
              }
              validTaskIds.push(task.id);
              continue;
            }
          }
        } else if (taskField === 'update_task_leader') {
          const newMembers = await User.find({
            where: { id: In(arrayValue), type: UserType.ORGANIZATION },
          });
          const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
          const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
          task.taskLeader = newMembers;
          const LeaderIds = task?.taskLeader?.map((item) => item?.id);
          const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
          let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
          let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
          if (leadersdifference?.length || taskleaderdifference?.length) {
            let taskactivity = new Activity();
            taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
            taskactivity.actorId = userId;
            taskactivity.type = ActivityType.TASK;
            taskactivity.typeId = task.id;
            taskactivity.remarks = `Past Task Leaders: ${
              taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
            }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
            await taskactivity.save();
          }
          // await task.save()
          validTaskIds.push(task.id);
          continue;
        } else if (taskField === 'add_task_leader') {
          const newMembers = await User.find({
            where: { id: In(arrayValue), type: UserType.ORGANIZATION },
          });
          const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
          const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
          task.taskLeader = [...task.taskLeader, ...newMembers];
          const LeaderIds = task?.taskLeader?.map((item) => item?.id);
          const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
          let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
          let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
          if (leadersdifference?.length || taskleaderdifference?.length) {
            let taskactivity = new Activity();
            taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
            taskactivity.actorId = userId;
            taskactivity.type = ActivityType.TASK;
            taskactivity.typeId = task.id;
            taskactivity.remarks = `Past Task Leaders: ${
              taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
            }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
            await taskactivity.save();
          }
          validTaskIds.push(task.id);
          continue;
        }
      }

      const validTasks = tasks.filter((task) => validTaskIds.includes(task.id));
      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkAddRemarks(userId: number, taskId: number, data: AddRemkarDto) {
    let activity = new Activity();
    activity.remarkType = data.remarkType;
    activity.remarks = data.remarks;
    activity.action = 'Remark Added';
    activity.actorId = userId;
    activity.type = ActivityType.TASK;
    activity.typeId = taskId;
    await activity.save();

    return activity;
  }

  async bulkUpdatetaskStatus(ids, taskField, newValue, userId, body) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    try {
      const tasks = await Task.find({
        where: { id: In(ids) },
        relations: [
          'recurringProfile',
          'approvalProcedures',
          'client',
          'approvalProcedures.approval',
          'approvalProcedures.approval.approvalLevels',
          'taskBudgetedHours',
          'subTasks',
          'members',
          'taskLeader',
          'udinTask',
          'clientGroup',
        ],
      });
      const deletedUsers = await createQueryBuilder(User, 'user')
        .leftJoin('user.organization', 'organization')
        .where('user.type = :type', { type: UserType.ORGANIZATION })
        .andWhere('user.status = :status', { status: UserStatus.DELETED })
        .andWhere('organization.id = :id', { id: user.organization.id })
        .getMany();
      const deletedIds = deletedUsers.map((item) => item.id);
      const validTaskIds = [];
      const errorsList = [];
      for (const task of tasks) {
        task['bulkUpdate'] = true;
        task['oldTaskSTatus'] = task.status;
        const oldTaskStatus = task.status;
        const newStatus = newValue;

        const taskMembersIds = task.members.map((member) => member.id);
        const hasCommonMembersId = deletedIds.some((id) => taskMembersIds.includes(id));
        const hasCommonTaskLeaderId = deletedIds.some((id) => taskMembersIds.includes(id));
        if (hasCommonMembersId) {
          const errorDes = `Can't Change Status Due to Task has Deleted Users`;
          errorsList.push({
            taskId: task.taskNumber,
            taskName: task.name,
            error: errorDes,
          });
          continue;
        }
        if (hasCommonTaskLeaderId) {
          const errorDes = `Can't Change Status Due to Task has Deleted Task leaders`;
          errorsList.push({
            taskId: task.taskNumber,
            taskName: task.name,
            error: errorDes,
          });
          continue;
        }
        if (newStatus && newStatus === TaskStatusEnum.COMPLETED && task.processInstanceId) {
          if (!task.approvalStatus?.[0]?.['completed']) {
            const taskStatusResult = `Task should be Approved at all levels before moving to Completed`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: taskStatusResult,
            });
            continue;
          } else if (task.approvalStatus?.[0]?.['completed']) {
            if (task.udinTask) {
              if (!task?.udinTask.udinNumber || !task?.udinTask.user || !task?.udinTask.date) {
                errorsList.push({
                  taskId: task.taskNumber,
                  taskName: task.name,
                  error: 'Provide UDIN Details before Completing the Task.',
                });
                continue;
              }
            }
          }
        } else if (
          task.approvalProcedures &&
          task.processInstanceId &&
          (newStatus === TaskStatusEnum.TODO ||
            newStatus === TaskStatusEnum.IN_PROGRESS ||
            newStatus === TaskStatusEnum.ON_HOLD) &&
          task.status === TaskStatusEnum.UNDER_REVIEW
        ) {
          if (task?.approvalStatus[0]?.['completed']) {
            const taskStatusResult = `As Approvals have been completed, you can't move this task to ${newStatus}`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: taskStatusResult,
            });
            continue;
          } else if (task?.approvalStatus[0]?.['progress']) {
            const taskStatusResult = `You can't move this task once the approval is in progress`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: taskStatusResult,
            });
          }
        } else if (
          task.subTasks.some((subTask) =>
            [
              TaskStatusEnum.TODO,
              TaskStatusEnum.IN_PROGRESS,
              TaskStatusEnum.ON_HOLD,
              TaskStatusEnum.UNDER_REVIEW,
            ].includes(subTask.status),
          ) &&
          newStatus === TaskStatusEnum.COMPLETED
        ) {
          const taskStatusResult = `All Sub-tasks Should Be Completed Before Completing The Main Task`;
          errorsList.push({
            taskId: task.taskNumber,
            taskName: task.name,
            error: taskStatusResult,
          });
          continue;
        } else if (!task.processInstanceId && newStatus === TaskStatusEnum.UNDER_REVIEW) {
          const taskStatusResult = `You can't move this task to Under Review as there are no Approvals for this Task`;
          errorsList.push({
            taskId: task.taskNumber,
            taskName: task.name,
            error: taskStatusResult,
          });
          continue;
        } else if (
          task.processInstanceId &&
          !task?.approvalStatus?.[0]['completed'] &&
          newStatus === TaskStatusEnum.UNDER_REVIEW
        ) {
          task.status = newStatus;
          task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          task.approvalStatus = [
            {
              status: `Approval Levels (${task?.approvalProcedures?.approval?.approvalLevels.length})`,
              completed: false,
            },
          ];
          this.eventEmitter.emit(Event_Actions.TASK_STATUS_TO_UNDER_REVIEW, { task, user });
          validTaskIds.push(task.id);
          continue;
        } else if (newValue === TaskStatusEnum.ON_HOLD) {
          task.status = newValue;
          task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          await this.bulkAddRemarks(userId, task.id, {
            remarkType: body.onHoldRemarkType,
            remarks: '',
          });
          validTaskIds.push(task.id);
          continue;
        } else if (newStatus === TaskStatusEnum.COMPLETED) {
          if (task.udinTask) {
            if (task?.udinTask?.udinNumber && task?.udinTask?.date && task?.udinTask?.user) {
              task.status = newStatus;
              task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
              validTaskIds.push(task.id);
              continue;
            } else {
              errorsList.push({
                taskId: task.taskNumber,
                taskName: task.name,
                error: 'Provide UDIN Details before Completing the Task.',
              });
              continue;
            }
          } else if (!task.udinTask) {
            task.status = newStatus;
            task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
            validTaskIds.push(task.id);
            continue;
          }
        } else {
          task.status = newValue;
          task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          if (oldTaskStatus !== newStatus) {
            let taskStatus = new TaskStatus();
            taskStatus.restore = newValue;
            taskStatus.status = newValue;
            taskStatus.task = task;
            taskStatus.user = user;
            await taskStatus.save();
          }
          validTaskIds.push(task.id);
          continue;
        }
      }

      for (const taskData of tasks) {
        const oldTaskSTatus = taskData['oldTaskSTatus'];
        if (taskData.status !== oldTaskSTatus) {
          let activity = new Activity();
          activity.action = Event_Actions.TASK_STATUS_UPDATED;
          activity.actorId = user.id;
          activity.type = ActivityType.TASK;
          activity.typeId = taskData?.id;
          activity.remarks = `"${taskData?.taskNumber}" Task moved from ${getTitle(
            oldTaskSTatus,
          )} to ${getTitle(taskData?.status)} by ${user.fullName}`;
          await activity.save();

          let clientactivity = new Activity();
          clientactivity.action = Event_Actions.TASK_STATUS_UPDATED;
          clientactivity.actorId = user.id;
          clientactivity.type = ActivityType.CLIENT;
          clientactivity.typeId = taskData?.client?.id;
          clientactivity.remarks = `"${taskData?.taskNumber}" Task moved from ${getTitle(
            oldTaskSTatus,
          )} to ${getTitle(taskData?.status)} by ${user.fullName}`;
          await clientactivity.save();
        }
      }

      const validTasks = tasks.filter((task) => validTaskIds.includes(task.id));
      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkTerminateAndDeleteTasks(ids, taskField, newStatus, userId) {
    try {
      const tasks = await Task.find({
        where: { id: In(ids) },
        relations: [
          'client',
          'subTasks',
          'taskLogHours',
          'parentTask',
          'user',
          'clientGroup',
          'members',
          'taskLeader',
        ],
      });
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const validTaskIds = [];
      const errorsList = [];
      const subtasksUpdate = [];

      for (const task of tasks) {
        task['bulkUpdate'] = true;
        if (newStatus === TaskStatusEnum.DELETED) {
          if (task?.taskLogHours?.length) {
            const lastTaskItem = task.taskLogHours[task.taskLogHours.length - 1];
            if (lastTaskItem.status !== 'stopped') {
              let taskLogHourLast = await LogHour.findOne({
                where: { id: lastTaskItem.id },
                relations: ['user'],
              });
              taskLogHourLast.status = TimerStatus.STOPPED;
              taskLogHourLast.completedDate = moment().format('YYYY-MM-DD');
              await taskLogHourLast.save();
            }
          }

          task.restore = task.status;
          task.status = TaskStatusEnum.DELETED;
          task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          task['userId'] = user.id;
          await task.save();

          let taskStatus = new TaskStatus();
          taskStatus.status = task.restore;
          taskStatus.restore = task.restore;
          taskStatus.task = task;
          taskStatus.user = user;
          await taskStatus.save();

          subtasksUpdate.push(
            ...task.subTasks.map((subTask) => {
              subTask.status = TaskStatusEnum.DELETED;
              subTask.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
              subTask['userId'] = user.id;
              return subTask.save();
            }),
          );

          validTaskIds.push(task.id);

          this.eventEmitter.emit(Event_Actions.TASK_DELETED, {
            userId,
            clientId: task.client?.id,
            task,
          });
        }
        if (newStatus === TaskStatusEnum.TERMINATED) {
          if (task?.taskLogHours?.length) {
            const lastTaskItem = task.taskLogHours[task.taskLogHours.length - 1];
            if (lastTaskItem.status !== 'stopped') {
              let taskLogHourLast = await LogHour.findOne({
                where: { id: lastTaskItem.id },
                relations: ['user'],
              });
              taskLogHourLast.status = TimerStatus.STOPPED;
              taskLogHourLast.completedDate = moment().format('YYYY-MM-DD');
              await taskLogHourLast.save();
            }
          }

          task.restore = task.status;
          task.status = TaskStatusEnum.TERMINATED;
          task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          task['userId'] = userId;
          await task.save();

          let taskStatus = new TaskStatus();
          taskStatus.status = task.restore;
          taskStatus.restore = task.restore;
          taskStatus.task = task;
          taskStatus.user = user;
          await taskStatus.save();

          subtasksUpdate.push(
            ...task.subTasks.map((subTask) => {
              subTask.status = TaskStatusEnum.TERMINATED;
              subTask.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
              subTask['userId'] = userId;
              return subTask.save();
            }),
          );

          validTaskIds.push(task.id);

          this.eventEmitter.emit(Event_Actions.TASK_TERMINATED, {
            userId,
            clientId: task.client?.id,
            task,
          });
        }
      }

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkTaskRemoveBudgetedHours(ids: number[], userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const tasks = await Task.find({
        where: { id: In(ids) },
        relations: ['members'],
      });
      const validTaskIds: number[] = [];
      const errorsList: any[] = [];

      for (const task of tasks) {
        task['bulkUpdate'] = true;
        task.budgetedhours = 0;
        if (task.status === TaskStatusEnum.DELETED || task.status === TaskStatusEnum.TERMINATED) {
          const errorDes = `Can't Change Due to Task ${getTitle(task.status)}`;
          errorsList.push({
            taskId: task.taskNumber,
            taskName: task.name,
            error: errorDes,
          });
          continue;
        }
        const budgetedHours = await BudgetedHours.find({
          where: { task: { id: task.id } },
          relations: ['task', 'user'],
        });

        for (const b of budgetedHours) {
          b.budgetedHours = 0;
          b.status = BudgetedHourStatus.INACTIVE;
        }

        if (budgetedHours.length > 0) {
          await BudgetedHours.save(budgetedHours);
        }

        validTaskIds.push(task.id);
      }

      if (validTaskIds.length > 0) {
        await Task.save(tasks);
      }

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkTaskRemoveUDIN(ids, userId, udinTask) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const tasks = await Task.find({
        where: { id: In(ids) },
        relations: ['members', 'udinTask'],
      });

      const validTaskIds = [];
      const errorsList = [];

      for (const task of tasks) {
        task['bulkUpdate'] = true;
        if (task.status === TaskStatusEnum.DELETED || task.status === TaskStatusEnum.TERMINATED) {
          const errorDes = `Can't Change Due to Task ${getTitle(task.status)}`;
          errorsList.push({
            taskId: task.taskNumber,
            taskName: task.name,
            error: errorDes,
          });
          continue;
        }

        task.isUdin = udinTask ? true : false;
        if (!udinTask) {
          if (task.udinTask) {
            let udinTaskData = task.udinTask;
            await udinTaskData.remove();
          }
        } else if (udinTask) {
          if (task.udinTask) {
            const taskdata = await Task.findOne({ where: { id: task.id } });
            const udinTaskData = task.udinTask;
            udinTaskData.task = taskdata;
            udinTaskData.udinTaskStatus = UdinTaskStatus.ACTIVE;
            await udinTaskData.save();
          } else {
            const udintaskk = new UdinTask();
            udintaskk.task = { id: task.id } as Task;
            udintaskk.udinTaskStatus = UdinTaskStatus.ACTIVE;
            udintaskk.userType = userType.ORGANIZATION;
            udintaskk.organization = user.organization;
            await udintaskk.save();
          }
        }
        task['userId'] = user.id;
        await task.save();

        validTaskIds.push(task.id);
      }

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkTaskReplaceUser(ids: number[], userId: number, existingUser: User, newUser: User) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const tasks = await Task.find({
        where: { id: In(ids) },
        relations: ['members', 'client', 'taskBudgetedHours', 'taskBudgetedHours.user'],
      });

      const validTaskIds: number[] = [];
      const errorsList: { taskId: any; taskName: string; error: string }[] = [];
      const budgetedToSave: BudgetedHours[] = [];

      for (const task of tasks) {
        try {
          if (task.status === TaskStatusEnum.DELETED || task.status === TaskStatusEnum.TERMINATED) {
            const errorDes = `Can't Change Due to Task ${getTitle(task.status)}`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: errorDes,
            });
            continue;
          }
          const existingUserFilter = task.members.filter((m) => m.id === existingUser.id);
          if (existingUserFilter.length === 0) {
            const errorDes = `Existing User is not a member of the task`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: errorDes,
            });
            continue;
          }
          task.members = task.members.filter((m) => m.id !== existingUser.id);
          if (!task.members.some((m) => m.id === newUser.id)) {
            task.members.push(newUser);
          }
          for (const bh of task.taskBudgetedHours ?? []) {
            if (bh.user?.id === existingUser.id) {
              const prevHours = bh.budgetedHours;
              bh.status = BudgetedHourStatus.INACTIVE;
              let oldInactive = task.taskBudgetedHours.find((b) => b.user?.id === newUser.id);
              if (oldInactive) {
                oldInactive.budgetedHours =
                  oldInactive.status === BudgetedHourStatus.ACTIVE
                    ? oldInactive.budgetedHours + prevHours
                    : prevHours;
                oldInactive.status = BudgetedHourStatus.ACTIVE;
                budgetedToSave.push(oldInactive);
              } else {
                const budgetedHours = BudgetedHours.create({
                  status: BudgetedHourStatus.ACTIVE,
                  budgetedHours: prevHours,
                  organization: user?.organization,
                  task,
                  user: newUser,
                  client: task?.client,
                });
                budgetedToSave.push(budgetedHours);
              }
            }
          }

          task['bulkUpdate'] = true;
          validTaskIds.push(task.id);
        } catch (err) {
          errorsList.push({ taskId: task.id, error: err.message, taskName: task.name });
        }
      }

      const validTasks = tasks.filter((t) => validTaskIds.includes(t.id));
      await Task.save(validTasks);
      await BudgetedHours.save(budgetedToSave);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async taskBulkUpdate(userId, body) {
    const {
      ids,
      selectedField,
      newValue,
      newMembers,
      newTaskLeaders,
      udinTask,
      existingUser,
      newUser,
    } = body;
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new BadRequestException('Select atleast one task');
    }

    if (typeof selectedField !== 'string' || newValue === undefined) {
      throw new BadRequestException('Invalid selected Field or new Value');
    }

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    const validTaskIdsArray = [];
    const errorsListArray = [];

    if (
      ['start_date', 'due_date', 'expected_completion_date', 'priority', 'billing_type'].includes(
        selectedField,
      )
    ) {
      const { errorsList, validTaskIds } = await this.bulkUpdateTaskFields(
        ids,
        selectedField,
        newValue,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['remove_budgeted_hours'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkTaskRemoveBudgetedHours(ids, userId);
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['update_new_with_existing_user'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkTaskReplaceUser(
        ids,
        userId,
        existingUser,
        newUser,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['update_udin'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkTaskRemoveUDIN(ids, userId, udinTask);
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['add_user', 'update_user', 'remove_user'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateTaskUsers(
        ids,
        selectedField,
        newMembers,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['add_task_leader', 'update_task_leader', 'remove_task_leader'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateTaskLeaders(
        ids,
        selectedField,
        newTaskLeaders,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (
      selectedField === 'status' &&
      [
        TaskStatusEnum.TODO,
        TaskStatusEnum.IN_PROGRESS,
        TaskStatusEnum.ON_HOLD,
        TaskStatusEnum.UNDER_REVIEW,
        TaskStatusEnum.COMPLETED,
      ].includes(newValue)
    ) {
      const { errorsList, validTaskIds } = await this.bulkUpdatetaskStatus(
        ids,
        selectedField,
        newValue,
        userId,
        body,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (
      selectedField === 'status' &&
      [TaskStatusEnum.DELETED, TaskStatusEnum.TERMINATED].includes(newValue)
    ) {
      const { errorsList, validTaskIds } = await this.bulkTerminateAndDeleteTasks(
        ids,
        selectedField,
        newValue,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    return { errorsListArray, validTaskIdsArray };
  }

  async recurringProfileBulkUpdate(userId, body) {
    const {
      ids,
      selectedField,
      newValue,
      newMembers,
      newLabels,
      newTaskLeaders,
      udinTask,
      recurringDetails,
      existingUser,
      newUser,
    } = body;
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new BadRequestException('Select atleast one Recurring Profile');
    }

    if (typeof selectedField !== 'string' || newValue === undefined) {
      throw new BadRequestException('Invalid selected Field or new Value');
    }

    const validTaskIdsArray = [];
    const errorsListArray = [];

    if (['priority', 'billing_type'].includes(selectedField)) {
      const { errorsList, validProfileIds: validTaskIds } =
        await this.bulkUpdateRecurringProfileFields(ids, selectedField, newValue, userId);
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['add_label', 'update_label', 'remove_label'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateLabels(
        ids,
        selectedField,
        newLabels,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['add_user', 'update_user', 'remove_user'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateRecurringUsers(
        ids,
        selectedField,
        newMembers,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['add_task_leader', 'update_task_leader', 'remove_task_leader'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateRecurringTaskLeaders(
        ids,
        selectedField,
        newTaskLeaders,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['update_approval'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateRecurringApprovals(
        ids,
        selectedField,
        newValue,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['update_udin'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateRecurringUDIN(
        ids,
        selectedField,
        udinTask,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['update_dates'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateRecurringDates(
        ids,
        selectedField,
        recurringDetails,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['terminate_recurring_profile'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkTerminateRecurringProfile(ids, userId);
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['remove_budgeted_hours'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkRemoveBudgetedHours(ids, userId);
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['update_new_with_existing_user'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkReplaceUser(
        ids,
        userId,
        existingUser,
        newUser,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    return { errorsListArray, validTaskIdsArray };
  }

  async bulkUpdateRecurringProfileFields(ids, taskField, value, userId) {
    try {
      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      let user = await User.findOne({
        where: {
          id: userId,
        },
      });

      let validProfileIds = [];
      const errorsList = [];

      for (const profile of recurringProfile) {
        let updatePayload;
        if (taskField === 'priority') {
          updatePayload = { priority: value };
        } else if (taskField === 'billing_type') {
          updatePayload = { billable: value === 'BILLABLE' ? true : false };
        }

        const taskIDs = profile?.tasks.map((item) => item.id);
        validProfileIds = [...taskIDs];
        await createQueryBuilder()
          .update(Task)
          .set(updatePayload)
          .where('id IN (:...ids)', { ids: taskIDs })
          .execute();
      }

      return { errorsList, validProfileIds };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateLabels(ids, taskField, arrayValue, userId) {
    try {
      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('task.labels', 'labels')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const validTaskIds = [];
      const errorsList = [];
      for (const profile of recurringProfile) {
        for (const task of profile?.tasks) {
          task['bulkUpdate'] = true;
          if (['add_label', 'update_label', 'remove_label'].includes(taskField)) {
            if (taskField === 'remove_label') {
              // if (task.labels.length > 1) {
              const newMembers = await Label.find({
                where: { id: In(arrayValue) },
              });
              const existingIds = task.labels.map((item) => item.id);
              const newIds = newMembers.map((item) => item.id);
              const nonExistingIds = newIds.filter((id) => !existingIds.includes(id));
              if (nonExistingIds?.length > 0) {
                const errorDes = 'Cannot remove a label which is not in the task';
                errorsList.push({
                  clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                  taskId: task.taskNumber,
                  taskName: task.name,
                  error: errorDes,
                });
                continue;
              } else if (nonExistingIds?.length === 0) {
                task.labels = task.labels.filter((member) => !arrayValue.includes(member.id));
                validTaskIds.push(task.id);
                continue;
              }
              // }
            } else if (taskField === 'update_label') {
              const newMembers = await Label.find({
                where: { id: In(arrayValue) },
              });
              task.labels = newMembers;
              validTaskIds.push(task.id);
              continue;
            } else if (taskField === 'add_label') {
              const newMembers = await Label.find({
                where: { id: In(arrayValue) },
              });
              task.labels = [...task.labels, ...newMembers];
              validTaskIds.push(task.id);
              continue;
            }
          }
        }
      }
      let validTasks = [];
      for (const profile of recurringProfile) {
        validTasks = [
          ...validTasks,
          ...profile?.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }
      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateRecurringUsers(ids, taskField, arrayValue, userId) {
    function handleText(text) {
      return text
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    try {
      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.members', 'members')
        .leftJoinAndSelect('task.labels', 'labels')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const validTaskIds = [];
      const errorsList = [];
      for (const profile of recurringProfile) {
        for (const task of profile.tasks) {
          task['bulkUpdate'] = true;
          if (['add_user', 'update_user', 'remove_user'].includes(taskField)) {
            if (task?.budgetedhours != 0 && taskField !== 'remove_user') {
              const errorDes = `Cannot ${handleText(
                taskField,
              )} because this task has Budgeted Hours`;
              errorsList.push({
                clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                taskId: task.taskNumber,
                taskName: task.name,
                error: errorDes,
              });
              continue;
            } else if (taskField === 'remove_user') {
              if (task.members.length === 1) {
                const errorDes = 'Task must contain atleast one member';
                errorsList.push({
                  clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                  taskId: task.taskNumber,
                  taskName: task.name,
                  error: errorDes,
                });
                continue;
              } else if (task.members.length > 1) {
                const newMembers = await User.find({
                  where: { id: In(arrayValue), type: UserType.ORGANIZATION },
                });
                const existingIds = task.members.map((item) => item.id);
                const newIds = newMembers.map((item) => item.id);
                const nonExistingIds = newIds.filter((id) => !existingIds.includes(id));
                if (nonExistingIds?.length > 0) {
                  const errorDes = 'Cannot remove a member who is not assigned in the task';
                  errorsList.push({
                    clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                    taskId: task.taskNumber,
                    taskName: task.name,
                    error: errorDes,
                  });
                  continue;
                } else if (nonExistingIds?.length === 0) {
                  const taskMemebersIds = task.members.map((item) => item?.id);
                  const taskmembersName = task.members.map((item) => item.fullName);
                  task.members = task.members.filter((member) => !arrayValue.includes(member.id));
                  if (task.members.length === 0) {
                    const errorDes = 'Task must contain atleast one member';
                    errorsList.push({
                      clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                      taskId: task.taskNumber,
                      taskName: task.name,
                      error: errorDes,
                    });
                    continue;
                  }
                  if (task?.budgetedhours != 0) {
                    const taskBudgetedHours = await BudgetedHours.find({
                      where: { task: task },
                      relations: ['user'],
                    });
                    for (let i of taskBudgetedHours) {
                      if (arrayValue.includes(i?.user?.id)) {
                        const previousBudgetedHours = i?.budgetedHours;
                        i.budgetedHours = 0;
                        i.status = BudgetedHourStatus.INACTIVE;
                        await i.save();
                        task.budgetedhours = task.budgetedhours - previousBudgetedHours;
                      }
                    }
                  }
                  validTaskIds.push(task.id);
                  const membersIds = task.members.map((item) => item?.id);
                  const membersName = task.members.map((item) => item.fullName);
                  let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
                  let taskmembersdifference = taskMemebersIds.filter(
                    (x) => !membersIds.includes(x),
                  );
                  if (membersdifference.length || taskmembersdifference.length) {
                    let taskactivity = new Activity();
                    taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
                    taskactivity.actorId = user.id;
                    taskactivity.type = ActivityType.TASK;
                    taskactivity.typeId = task.id;
                    taskactivity.remarks = `Past Members: ${taskmembersName.join(
                      ', ',
                    )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
                    await taskactivity.save();
                  }
                  continue;
                }
              }
            } else if (taskField === 'update_user') {
              const newMembers = await User.find({
                where: { id: In(arrayValue), type: UserType.ORGANIZATION },
              });
              const taskMemebersIds = task.members.map((item) => item?.id);
              const taskmembersName = task.members.map((item) => item.fullName);
              task.members = newMembers;
              const membersIds = task.members.map((item) => item?.id);
              const membersName = task.members.map((item) => item.fullName);
              let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
              let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));
              if (membersdifference.length || taskmembersdifference.length) {
                let taskactivity = new Activity();
                taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
                taskactivity.actorId = user.id;
                taskactivity.type = ActivityType.TASK;
                taskactivity.typeId = task.id;
                taskactivity.remarks = `Past Members: ${taskmembersName.join(
                  ', ',
                )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
                await taskactivity.save();
              }
              validTaskIds.push(task.id);
              continue;
            } else if (taskField === 'add_user') {
              const newMembers = await User.find({
                where: { id: In(arrayValue), type: UserType.ORGANIZATION },
              });
              const taskMemebersIds = task.members.map((item) => item?.id);
              const taskmembersName = task.members.map((item) => item.fullName);
              task.members = [...task.members, ...newMembers];
              const membersIds = task.members.map((item) => item?.id);
              const membersName = task.members.map((item) => item.fullName);
              let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
              let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));
              if (membersdifference.length || taskmembersdifference.length) {
                let taskactivity = new Activity();
                taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
                taskactivity.actorId = user.id;
                taskactivity.type = ActivityType.TASK;
                taskactivity.typeId = task.id;
                taskactivity.remarks = `Past Members: ${taskmembersName.join(
                  ', ',
                )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
                await taskactivity.save();
              }
              validTaskIds.push(task.id);
              continue;
            }
          }
        }
      }
      let validTasks = [];
      for (const profile of recurringProfile) {
        validTasks = [
          ...validTasks,
          ...profile?.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }
      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateRecurringTaskLeaders(ids, taskField, arrayValue, userId) {
    try {
      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.taskLeader', 'taskLeader')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('task.labels', 'labels')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds = [];
      const errorsList = [];

      for (const profile of recurringProfile) {
        for (const task of profile.tasks) {
          task['bulkUpdate'] = true;
          if (taskField === 'remove_task_leader') {
            if (task?.taskLeader?.length > 0) {
              const newMembers = await User.find({
                where: { id: In(arrayValue), type: UserType.ORGANIZATION },
              });
              const existingIds = task?.taskLeader?.map((item) => item.id);
              const newIds = newMembers.map((item) => item.id);
              const nonExistingIds = newIds.filter((id) => !existingIds.includes(id));
              if (nonExistingIds?.length > 0) {
                const errorDes = 'Cannot remove a leader who is not assigned in the task';
                errorsList.push({
                  clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                  taskId: task.taskNumber,
                  taskName: task.name,
                  error: errorDes,
                });
                continue;
              } else if (nonExistingIds?.length === 0) {
                const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
                const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
                task.taskLeader = task.taskLeader.filter(
                  (member) => !arrayValue.includes(member.id),
                );
                const LeaderIds = task?.taskLeader?.map((item) => item?.id);
                const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
                let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
                let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
                if (leadersdifference?.length || taskleaderdifference?.length) {
                  let taskactivity = new Activity();
                  taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
                  taskactivity.actorId = userId;
                  taskactivity.type = ActivityType.TASK;
                  taskactivity.typeId = task.id;
                  taskactivity.remarks = `Past Task Leaders: ${
                    taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
                  }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
                  await taskactivity.save();
                }
                validTaskIds.push(task.id);
                continue;
              }
            }
          } else if (taskField === 'update_task_leader') {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
            const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
            task.taskLeader = newMembers;
            const LeaderIds = task?.taskLeader?.map((item) => item?.id);
            const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
            let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
            let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
            if (leadersdifference?.length || taskleaderdifference?.length) {
              let taskactivity = new Activity();
              taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
              taskactivity.actorId = userId;
              taskactivity.type = ActivityType.TASK;
              taskactivity.typeId = task.id;
              taskactivity.remarks = `Past Task Leaders: ${
                taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
              }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
              await taskactivity.save();
            }
            validTaskIds.push(task.id);
            // await task.save()
            continue;
          } else if (taskField === 'add_task_leader') {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
            const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
            task.taskLeader = [...task.taskLeader, ...newMembers];
            const LeaderIds = task?.taskLeader?.map((item) => item?.id);
            const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
            let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
            let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
            if (leadersdifference?.length || taskleaderdifference?.length) {
              let taskactivity = new Activity();
              taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
              taskactivity.actorId = userId;
              taskactivity.type = ActivityType.TASK;
              taskactivity.typeId = task.id;
              taskactivity.remarks = `Past Task Leaders: ${
                taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
              }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
              await taskactivity.save();
            }
            validTaskIds.push(task.id);
            continue;
          }
        }
      }
      let validTasks = [];
      for (const profile of recurringProfile) {
        validTasks = [
          ...validTasks,
          ...profile?.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }

      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateRecurringApprovals(ids, taskField, newValue, userId) {
    try {
      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds = [];
      const errorsList = [];

      for (const profile of recurringProfile) {
        for (const task of profile.tasks) {
          task['bulkUpdate'] = true;
          task.approvalProcedures = newValue;
          validTaskIds.push(task.id);
        }
      }
      let validTasks = [];
      for (const profile of recurringProfile) {
        validTasks = [
          ...validTasks,
          ...profile?.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }
      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateRecurringUDIN(ids, taskField, udinTask, userId) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('task.udinTask', 'udinTask')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds = [];
      const errorsList = [];

      for (const profile of recurringProfile) {
        for (const task of profile.tasks) {
          task['bulkUpdate'] = true;
          task.isUdin = udinTask ? true : false;
          if (!udinTask) {
            if (task.udinTask) {
              let udinTask = task.udinTask;
              udinTask.task = task;
              udinTask.udinTaskStatus = UdinTaskStatus.INACTIVE;
              await udinTask.save();
            }
          } else if (udinTask) {
            if (task.udinTask) {
              let udinTask = task.udinTask;
              udinTask.task = task;
              udinTask.udinTaskStatus = UdinTaskStatus.ACTIVE;
              await udinTask.save();
            } else {
              let udintask = new UdinTask();
              udintask.task = task;
              udintask.userType = userType.ORGANIZATION;
              udintask.organization = user.organization;
              udintask.recurringProfile = profile;
              udintask.udinTaskStatus = UdinTaskStatus.ACTIVE;
              await udintask.save();
            }
          }
          validTaskIds.push(task.id);
        }
      }
      let validTasks = [];
      for (const profile of recurringProfile) {
        validTasks = [
          ...validTasks,
          ...profile?.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }
      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkTerminateRecurringProfile(ids, userId) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('task.service', 'service')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds = [];
      const errorsList = [];
      const activities = [];

      for (const profile of recurringProfile) {
        for (const task of profile.tasks) {
          task['bulkUpdate'] = true;
          task.status = TaskStatusEnum.TERMINATED;
          task.recurringStatus = TaskRecurringStatus.TERMINATED;
          validTaskIds.push(task.id);
        }
        let activity = new Activity();
        activity.action = Event_Actions.RECURRING_PROFILE_TERMINATED;
        activity.actorId = user.id;
        activity.type = profile.tasks[profile.tasks.length - 1].client
          ? ActivityType.CLIENT
          : ActivityType.CLIENT_GROUP;
        activity.typeId = profile.tasks[profile.tasks.length - 1].client
          ? profile.tasks[profile.tasks.length - 1]?.client?.id
          : profile.tasks[profile.tasks.length - 1]?.clientGroup?.id;
        activity.remarks = `"${
          profile.tasks[profile.tasks.length - 1]?.service?.name || profile?.name
        }" Recurring Profile Terminated by ${user.fullName}`;
        activities.push(activity);
      }
      let validTasks = [];
      for (const profile of recurringProfile) {
        validTasks = [
          ...validTasks,
          ...profile?.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }
      await Task.save(validTasks);
      await Activity.save(activities);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkRemoveBudgetedHours(ids: number[], userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      if (!user) {
        throw new BadRequestException('User not found');
      }

      const recurringProfiles = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoin('task.service', 'service')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();
      const validTaskIds: number[] = [];
      const errorsList: string[] = [];

      for (const profile of recurringProfiles) {
        for (const task of profile.tasks) {
          task['bulkUpdate'] = true;
          task.budgetedhours = 0;

          const budgetedHoursList = await BudgetedHours.createQueryBuilder('taskBudgetedHours')
            .leftJoinAndSelect('taskBudgetedHours.task', 'task')
            .leftJoinAndSelect('taskBudgetedHours.user', 'user')
            .where('task.id = :taskId', { taskId: task.id })
            .getMany();

          for (const bh of budgetedHoursList) {
            bh.budgetedHours = 0;
            bh.status = BudgetedHourStatus.INACTIVE;
            await BudgetedHours.save(bh);
          }
          validTaskIds.push(task.id);
        }
        if (profile.tasks.length > 0) {
          const lastTask = profile.tasks[profile.tasks.length - 1];
          const activity = new Activity();
          activity.action = Event_Actions.RECURRING_PROFILE_UPDATED;
          activity.actorId = user.id;
          activity.type = lastTask.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
          activity.typeId = lastTask.client ? lastTask.client.id : lastTask.clientGroup.id;
          activity.remarks = `"${profile.name}" Recurring Profile Updated by ${user.fullName}`;
          await Activity.save(activity);
        }
      }

      let validTasks: Task[] = [];
      for (const profile of recurringProfiles) {
        validTasks.push(...profile.tasks.filter((task) => validTaskIds.includes(task.id)));
      }

      if (validTasks.length > 0) {
        await Task.save(validTasks);
      }

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkReplaceUser(ids: number[], userId: number, existingUser: User, newUser: User) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      if (!user) {
        throw new BadRequestException('User not found');
      }
      const recurringProfiles = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.members', 'members')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoin('task.service', 'service')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds: number[] = [];
      const errorsList: any[] = [];

      for (const profile of recurringProfiles) {
        const existingUserFilter = profile.tasks?.[0]?.members.filter(
          (m) => m.id === existingUser.id,
        );
        if (existingUserFilter.length === 0) {
          const errorDes = `Existing User is not a member of the task`;
          errorsList.push({
            clientId: profile.tasks?.[0]?.client
              ? profile.tasks?.[0]?.client?.clientId
              : profile.tasks?.[0]?.clientGroup?.clientId,
            taskName: profile.tasks?.[0].name,
            error: errorDes,
          });
          continue;
        }
        for (const task of profile.tasks) {
          task.members = task.members.filter((member) => member.id !== existingUser.id);
          task.members.push(newUser);
          if (task?.budgetedhours && task.budgetedhours > 0) {
            const taskBudgetedHours = await BudgetedHours.find({
              where: { task: task },
              relations: ['user'],
            });
            for (const bh of taskBudgetedHours) {
              if (bh.user?.id === existingUser.id) {
                const previousBudgetedHours = bh.budgetedHours;
                bh.status = BudgetedHourStatus.INACTIVE;
                bh.budgetedHours = 0;
                await bh.save();
                let newUserBH = await BudgetedHours.findOne({
                  where: { task: task, user: newUser },
                });
                if (newUserBH) {
                  newUserBH.budgetedHours =
                    newUserBH.status === BudgetedHourStatus.ACTIVE
                      ? newUserBH.budgetedHours + previousBudgetedHours
                      : previousBudgetedHours;
                  newUserBH.status = BudgetedHourStatus.ACTIVE;
                  await newUserBH.save();
                } else {
                  const budgetedHours = new BudgetedHours();
                  budgetedHours.status = BudgetedHourStatus.ACTIVE;
                  budgetedHours.budgetedHours = previousBudgetedHours;
                  budgetedHours.organization = user.organization;
                  budgetedHours.task = task;
                  budgetedHours.user = newUser;
                  budgetedHours.client = task.client;
                  await budgetedHours.save();
                }
              }
            }
          }

          task['bulkUpdate'] = true;
          validTaskIds.push(task.id);
        }
      }

      let validTasks: Task[] = [];
      for (const profile of recurringProfiles) {
        validTasks = [
          ...validTasks,
          ...profile.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }

      if (validTasks.length > 0) {
        await Task.save(validTasks);
      }

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateRecurringDates(ids, taskField, recurringDetails, userId) {
    try {
      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();
      const validTaskIds = [];
      const errorsList = [];
      const splitFinancialYear = recurringDetails.financialYear.split('-');
      const futurefinancialyaer = splitFinancialYear
        .map((item) => String(parseInt(item) + 1))
        .join('-');

      const oldRecurringDetails = JSON.parse(JSON.stringify(recurringDetails?.dates || []));

      const newOldRecurringDetails = oldRecurringDetails?.map((item) => {
        const newPeriodArray = item.period.split(' ');
        let newPeriod = '';

        if (recurringDetails?.frequency === 'monthly') {
          newPeriod = `${newPeriodArray[0]} ${String(parseInt(newPeriodArray[1]) + 1)}`;
        } else if (
          recurringDetails?.frequency === 'quarterly' ||
          recurringDetails?.frequency === 'half_yearly'
        ) {
          let newYear = parseInt(newPeriodArray[newPeriodArray.length - 1]) + 1;
          newPeriod = newPeriodArray.slice(0, -1).join(' ') + ' ' + String(newYear);
        } else if (recurringDetails?.frequency === 'yearly') {
          newPeriod = `${newPeriodArray[0]} ${String(
            newPeriodArray[1]
              .split('-')
              .map((yearWise) => parseInt(yearWise) + 1)
              .join('-'),
          )}`;
        }

        item.period = newPeriod;

        item.startDate = moment(item.startDate, 'YYYY-MM-DD').add(1, 'year').format('YYYY-MM-DD');
        item.dueDate = moment(item.dueDate, 'YYYY-MM-DD').add(1, 'year').format('YYYY-MM-DD');
        item.expectedCompletionDate = item.expectedCompletionDate
          ? moment(item.expectedCompletionDate, 'YYYY-MM-DD').add(1, 'year').format('YYYY-MM-DD')
          : null;

        return item;
      });

      for (const profile of recurringProfile) {
        for (const task of profile.tasks) {
          if (task.financialYear === recurringDetails.financialYear) {
            for (let i of recurringDetails?.dates) {
              if (task.name.includes(i.period) && recurringDetails?.frequency !== 'yearly') {
                task.taskStartDate = i.startDate;
                task.dueDate = i.dueDate;
                task.expectedCompletionDate = i.expectedCompletionDate;
                validTaskIds.push(task.id);
              } else if (
                recurringDetails?.frequency === 'yearly' &&
                task.name.includes(recurringDetails.financialYear)
              ) {
                task.taskStartDate = i.startDate;
                task.dueDate = i.dueDate;
                task.expectedCompletionDate = i.expectedCompletionDate;
                validTaskIds.push(task.id);
              }
            }
          } else if (task.financialYear === futurefinancialyaer) {
            for (let i of newOldRecurringDetails) {
              if (task.name.includes(i.period) && recurringDetails?.frequency !== 'yearly') {
                task.taskStartDate = i.startDate;
                task.dueDate = i.dueDate;
                task.expectedCompletionDate = i.expectedCompletionDate;
                validTaskIds.push(task.id);
              } else if (
                recurringDetails?.frequency === 'yearly' &&
                task.name.includes(futurefinancialyaer)
              ) {
                task.taskStartDate = i.startDate;
                task.dueDate = i.dueDate;
                task.expectedCompletionDate = i.expectedCompletionDate;
                validTaskIds.push(task.id);
              }
            }
          }
          task['bulkUpdate'] = true;
        }
      }
      let validTasks = [];
      for (const profile of recurringProfile) {
        validTasks = [
          ...validTasks,
          ...profile?.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }

      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async nonRecurringBulkUpdate(userId, body) {
    const {
      ids,
      selectedField,
      newValue,
      newMembers,
      newLabels,
      newTaskLeaders,
      udinTask,
      recurringDetails,
      existingUser,
      newUser,
    } = body;
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new BadRequestException('Select atleast one Tasks');
    }

    if (typeof selectedField !== 'string' || newValue === undefined) {
      throw new BadRequestException('Invalid selected Field or new Value');
    }

    const validTaskIdsArray = [];
    const errorsListArray = [];

    if (
      ['start_date', 'due_date', 'expected_completion_date', 'priority', 'billing_type'].includes(
        selectedField,
      )
    ) {
      const { errorsList, validTaskIds } = await this.bulkUpdateNonRecurringTaskFields(
        ids,
        selectedField,
        newValue,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['add_user', 'update_user', 'remove_user'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateNonRecurringTaskUsers(
        ids,
        selectedField,
        newMembers,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['add_task_leader', 'update_task_leader', 'remove_task_leader'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateTaskLeaders(
        ids,
        selectedField,
        newTaskLeaders,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['update_approval'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkUpdateNonRecurringApprovals(
        ids,
        selectedField,
        newValue,
        userId,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['terminate_upcoming_task'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkTerminateNonRecurringTasks(ids, userId);
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    if (['update_new_with_existing_user'].includes(selectedField)) {
      const { errorsList, validTaskIds } = await this.bulkTaskReplaceUser(
        ids,
        userId,
        existingUser,
        newUser,
      );
      validTaskIdsArray.push(...validTaskIds);
      errorsListArray.push(...errorsList);
    }

    return { errorsListArray, validTaskIdsArray };
  }

  async bulkUpdateNonRecurringTaskFields(ids, taskField, value, userId) {
    try {
      const tasks = await Task.find({
        where: { id: In(ids) },
      });
      let user = await User.findOne({
        where: {
          id: userId,
        },
      });

      const validTaskIds = [];
      const errorsList = [];
      if (taskField === 'start_date' || taskField === 'expected_completion_date') {
        for (const task of tasks) {
          if (taskField === 'start_date') {
            if (task.expectedCompletionDate && value && !(task.expectedCompletionDate >= value)) {
              const errorDes = 'Start Date should be before the Expected Completion Date';
              errorsList.push({
                taskId: task.taskNumber,
                taskName: task.name,
                error: errorDes,
              });
              continue;
            } else {
              validTaskIds.push(task.id);
              if (task.taskStartDate !== moment(value).format('YYYY-MM-DD')) {
                let activity = new Activity();
                activity.action = Event_Actions.START_DATE_UPDATED;
                activity.actorId = userId;
                activity.type = ActivityType.TASK;
                activity.typeId = task?.id;
                activity.remarks = `"${task?.taskNumber}" Task Start Date Changed from ${moment(
                  value,
                ).format('DD-MM-YYYY')} to ${moment(task.taskStartDate).format('DD-MM-YYYY')} by ${
                  user.fullName
                }`;
                activity.remarks = `"${task?.taskNumber}" Task Start Date Changed from ${moment(
                  value,
                ).format('DD-MM-YYYY')} to ${moment(task.taskStartDate).format('DD-MM-YYYY')} by ${
                  user.fullName
                }`;
                await activity.save();
              }
              continue;
            }
          } else if (taskField === 'expected_completion_date') {
            if (value && task.taskStartDate && !(value >= task.taskStartDate)) {
              const errorDes = 'Expected Completion Date should be after the Start Date';
              errorsList.push({
                taskId: task.taskNumber,
                taskName: task.name,
                error: errorDes,
              });
              continue;
            } else if (value && task.taskStartDate && value >= task.taskStartDate) {
              validTaskIds.push(task.id);
              if (
                moment(task.expectedCompletionDate).format('YYYY-MM-DD') !==
                moment(value).format('YYYY-MM-DD')
              ) {
                let activity = new Activity();
                activity.action = Event_Actions.EXPECTED_COMPLETEION_DATE_CHANGED;
                activity.actorId = userId;
                activity.type = ActivityType.TASK;
                activity.typeId = task?.id;
                activity.remarks = `"${
                  task?.taskNumber
                }" Task Expected Completion Date Changed from ${moment(value).format(
                  'DD-MM-YYYY',
                )} to ${moment(task.expectedCompletionDate).format('DD-MM-YYYY')} by ${
                  user.fullName
                }`;
                await activity.save();
              }
              continue;
            }
          }
        }
      } else {
        validTaskIds.push(...ids);
        for (const task of tasks) {
          if (task.dueDate !== moment(value).format('YYYY-MM-DD')) {
            let activity = new Activity();
            activity.action = Event_Actions.STATUTORY_DUE_DATE_CHANGED;
            activity.actorId = userId;
            activity.type = ActivityType.TASK;
            activity.typeId = task?.id;
            activity.remarks = `"${task?.taskNumber}" Task Statutory Due Date Changed from ${moment(
              value,
            ).format('DD-MM-YYYY')} to ${moment(task.dueDate).format('DD-MM-YYYY')} by ${
              user.fullName
            }`;
            await activity.save();
          }
          if (task.priority !== value) {
            let activity = new Activity();
            activity.action = Event_Actions.PRIORITY_UPDATED;
            activity.actorId = userId;
            activity.type = ActivityType.TASK;
            activity.typeId = task?.id;
            activity.remarks = `"${task?.taskNumber}" Task Priority Changed from "${getTitle(
              task.priority,
            )}" to "${getTitle(task.priority)}" by ${user.fullName}`;
            await activity.save();
          }
          if ((task.billable ? 'BILLABLE' : 'NON_BILLABLE') !== value) {
            let activity = new Activity();
            activity.action = Event_Actions.BILLING_TYPE_CHANGED;
            activity.actorId = userId;
            activity.type = ActivityType.TASK;
            activity.typeId = task?.id;
            activity.remarks = `"${task?.taskNumber}" Task Billing Type Changed from ${
              task?.billable ? 'Billable' : 'Non-Billable'
            } to ${value === 'BILLABLE' ? 'Billable' : 'Non-Billable'} by ${user.fullName}`;
            await activity.save();
          }
        }
      }

      if (validTaskIds.length > 0) {
        let updatePayload;
        if (taskField === 'priority') {
          updatePayload = { priority: value };
        } else if (taskField === 'start_date') {
          updatePayload = { taskStartDate: value };
        } else if (taskField === 'due_date') {
          updatePayload = { dueDate: value };
        } else if (taskField === 'billing_type') {
          updatePayload = { billable: value === 'BILLABLE' ? true : false };
        } else if (taskField === 'expected_completion_date') {
          updatePayload = { expectedCompletionDate: value };
        }
        await createQueryBuilder()
          .update(Task)
          .set(updatePayload)
          .where('id IN (:...ids)', { ids: validTaskIds })
          .execute();
      } else {
      }
      return { errorsList, validTaskIds };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateNonRecurringTaskUsers(ids, taskField, arrayValue, userId) {
    function handleText(text) {
      return text
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    try {
      const tasks = await Task.find({
        where: { id: In(ids) },
        relations: ['members', 'taskBudgetedHours'],
      });

      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const validTaskIds = [];
      const errorsList = [];
      for (const task of tasks) {
        task['bulkUpdate'] = true;
        if (['add_user', 'update_user', 'remove_user'].includes(taskField)) {
          if (task?.budgetedhours > 0 && taskField !== 'remove_user') {
            const errorDes = `Cannot ${handleText(taskField)} because this task has Budgeted Hours`;
            errorsList.push({
              taskId: task.taskNumber,
              taskName: task.name,
              error: errorDes,
            });
            continue;
          } else if (taskField === 'remove_user') {
            if (task.members.length === 1) {
              const errorDes = 'Task must contain atleast one member';
              errorsList.push({
                taskId: task.taskNumber,
                taskName: task.name,
                error: errorDes,
              });
              continue;
            } else if (task.members.length > 1) {
              const newMembers = await User.find({
                where: { id: In(arrayValue), type: UserType.ORGANIZATION },
              });
              const existingIds = task.members.map((item) => item.id);
              const newIds = newMembers.map((item) => item.id);
              const nonExistingIds = newIds.filter((id) => !existingIds.includes(id));
              if (nonExistingIds?.length > 0) {
                const errorDes = 'Cannot remove a member who is not assigned in the task';
                errorsList.push({
                  taskId: task.taskNumber,
                  taskName: task.name,
                  error: errorDes,
                });
                continue;
              } else if (nonExistingIds?.length === 0) {
                const taskMemebersIds = task.members.map((item) => item?.id);
                const taskmembersName = task.members.map((item) => item.fullName);
                task.members = task.members.filter((member) => !arrayValue.includes(member.id));
                if (task.members.length === 0) {
                  const errorDes = 'Task must contain atleast one member';
                  errorsList.push({
                    clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                    taskId: task.taskNumber,
                    taskName: task.name,
                    error: errorDes,
                  });
                  continue;
                }
                if (task?.budgetedhours != 0) {
                  const taskBudgetedHours = await BudgetedHours.find({
                    where: { task: task },
                    relations: ['user'],
                  });
                  for (let i of taskBudgetedHours) {
                    if (arrayValue.includes(i?.user?.id)) {
                      const previousBudgetedHours = i?.budgetedHours;
                      i.status = BudgetedHourStatus.INACTIVE;
                      await i.save();
                      task.budgetedhours = task.budgetedhours - previousBudgetedHours;
                    }
                  }
                }
                validTaskIds.push(task.id);
                const membersIds = task.members.map((item) => item?.id);
                const membersName = task.members.map((item) => item.fullName);
                let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
                let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));
                if (membersdifference.length || taskmembersdifference.length) {
                  let taskactivity = new Activity();
                  taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
                  taskactivity.actorId = user.id;
                  taskactivity.type = ActivityType.TASK;
                  taskactivity.typeId = task.id;
                  taskactivity.remarks = `Past Members: ${taskmembersName.join(
                    ', ',
                  )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
                  await taskactivity.save();
                }
                continue;
              }
            }
          } else if (taskField === 'update_user') {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const taskMemebersIds = task.members.map((item) => item?.id);
            const taskmembersName = task.members.map((item) => item.fullName);
            task.members = newMembers;
            const membersIds = task.members.map((item) => item?.id);
            const membersName = task.members.map((item) => item.fullName);
            let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
            let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));
            if (membersdifference.length || taskmembersdifference.length) {
              let taskactivity = new Activity();
              taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
              taskactivity.actorId = user.id;
              taskactivity.type = ActivityType.TASK;
              taskactivity.typeId = task.id;
              taskactivity.remarks = `Past Members: ${taskmembersName.join(
                ', ',
              )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
              await taskactivity.save();
            }
            validTaskIds.push(task.id);
            continue;
          } else if (taskField === 'add_user') {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const taskMemebersIds = task.members.map((item) => item?.id);
            const taskmembersName = task.members.map((item) => item.fullName);
            task.members = [...task.members, ...newMembers];
            const membersIds = task.members.map((item) => item?.id);
            const membersName = task.members.map((item) => item.fullName);
            let membersdifference = membersIds.filter((x) => !taskMemebersIds.includes(x));
            let taskmembersdifference = taskMemebersIds.filter((x) => !membersIds.includes(x));
            if (membersdifference.length || taskmembersdifference.length) {
              let taskactivity = new Activity();
              taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
              taskactivity.actorId = user.id;
              taskactivity.type = ActivityType.TASK;
              taskactivity.typeId = task.id;
              taskactivity.remarks = `Past Members: ${taskmembersName.join(
                ', ',
              )}\n\n\n\n\nCurrent Members: ${membersName.join(', ')}`;
              await taskactivity.save();
            }
            validTaskIds.push(task.id);
            continue;
          }
        }
      }
      const validTasks = tasks.filter((task) => validTaskIds.includes(task.id));
      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateNonRecurringTaskLeaders(ids, taskField, arrayValue, userId) {
    try {
      const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.taskLeader', 'taskLeader')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('task.labels', 'labels')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds = [];
      const errorsList = [];

      for (const profile of recurringProfile) {
        for (const task of profile.tasks) {
          task['bulkUpdate'] = true;
          if (taskField === 'remove_task_leader') {
            if (task?.taskLeader?.length > 0) {
              const newMembers = await User.find({
                where: { id: In(arrayValue), type: UserType.ORGANIZATION },
              });
              const existingIds = task?.taskLeader?.map((item) => item.id);
              const newIds = newMembers.map((item) => item.id);
              const nonExistingIds = newIds.filter((id) => !existingIds.includes(id));
              if (nonExistingIds?.length > 0) {
                const errorDes = 'Cannot remove a leader who is not assigned in the task';
                errorsList.push({
                  clientId: task.client ? task?.client?.clientId : task?.clientGroup?.clientId,
                  taskId: task.taskNumber,
                  taskName: task.name,
                  error: errorDes,
                });
                continue;
              } else if (nonExistingIds?.length === 0) {
                const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
                const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
                task.taskLeader = task.taskLeader.filter(
                  (member) => !arrayValue.includes(member.id),
                );
                const LeaderIds = task?.taskLeader?.map((item) => item?.id);
                const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
                let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
                let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
                if (leadersdifference?.length || taskleaderdifference?.length) {
                  let taskactivity = new Activity();
                  taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
                  taskactivity.actorId = userId;
                  taskactivity.type = ActivityType.TASK;
                  taskactivity.typeId = task.id;
                  taskactivity.remarks = `Past Task Leaders: ${
                    taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
                  }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
                  await taskactivity.save();
                }
                validTaskIds.push(task.id);
                continue;
              }
            }
          } else if (taskField === 'update_task_leader') {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
            const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
            task.taskLeader = newMembers;
            const LeaderIds = task?.taskLeader?.map((item) => item?.id);
            const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
            let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
            let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
            if (leadersdifference?.length || taskleaderdifference?.length) {
              let taskactivity = new Activity();
              taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
              taskactivity.actorId = userId;
              taskactivity.type = ActivityType.TASK;
              taskactivity.typeId = task.id;
              taskactivity.remarks = `Past Task Leaders: ${
                taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
              }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
              await taskactivity.save();
            }
            validTaskIds.push(task.id);
            // await task.save()
            continue;
          } else if (taskField === 'add_task_leader') {
            const newMembers = await User.find({
              where: { id: In(arrayValue), type: UserType.ORGANIZATION },
            });
            const taskLeaderIds = task?.taskLeader?.map((item) => item?.id);
            const taskLeadersName = task?.taskLeader?.map((item) => item?.fullName);
            task.taskLeader = [...task.taskLeader, ...newMembers];
            const LeaderIds = task?.taskLeader?.map((item) => item?.id);
            const LeaderName = task?.taskLeader?.map((item) => item?.fullName);
            let leadersdifference = LeaderIds?.filter((x) => !taskLeaderIds?.includes(x));
            let taskleaderdifference = taskLeaderIds?.filter((x) => !LeaderIds?.includes(x));
            if (leadersdifference?.length || taskleaderdifference?.length) {
              let taskactivity = new Activity();
              taskactivity.action = Event_Actions.TASK_LEADERS_UPDATED;
              taskactivity.actorId = userId;
              taskactivity.type = ActivityType.TASK;
              taskactivity.typeId = task.id;
              taskactivity.remarks = `Past Task Leaders: ${
                taskLeadersName?.length ? taskLeadersName.join(', ') : 'NA'
              }\nCurrent Task leaders: ${LeaderName?.length ? LeaderName.join(', ') : 'NA'}`;
              await taskactivity.save();
            }
            validTaskIds.push(task.id);
            continue;
          }
        }
      }
      let validTasks = [];
      for (const profile of recurringProfile) {
        validTasks = [
          ...validTasks,
          ...profile?.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }

      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkUpdateNonRecurringApprovals(ids, taskField, newValue, userId) {
    try {
      const tasks = await Task.createQueryBuilder('task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .where('task.id IN (:...ids)', { ids })
        .andWhere('task.recurring is not true')
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds = [];
      const errorsList = [];

      for (const task of tasks) {
        task['bulkUpdate'] = true;
        task.approvalProcedures = newValue;
        if (!newValue) {
          task.approvalStatus = null;
          task.processInstanceId = null;
        }
        validTaskIds.push(task.id);
        await task.save();
      }

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkTerminateNonRecurringTasks(ids, userId) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const tasks = await Task.createQueryBuilder('task')
        .where('task.id IN (:...ids)', { ids })
        .andWhere('task.recurring is not true')
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds = [];
      const errorsList = [];
      const activities = [];

      for (const task of tasks) {
        task['bulkUpdate'] = true;
        task.status = TaskStatusEnum.TERMINATED;
        task.recurringStatus = TaskRecurringStatus.TERMINATED;
        validTaskIds.push(task.id);
        await task.save();
      }
      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async bulkReplaceNonRecurringUser(ids, userId, existingUser, newUser) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const recurringProfiles = await RecurringProfile.createQueryBuilder('recurringProfile')
        .leftJoinAndSelect('recurringProfile.tasks', 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.members', 'members')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoin('task.service', 'service')
        .where('recurringProfile.id IN (:...ids)', { ids })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();

      const validTaskIds: number[] = [];
      const errorsList: any[] = [];

      for (const profile of recurringProfiles) {
        const existingUserFilter = profile.tasks?.[0]?.members.filter(
          (m) => m.id === existingUser.id,
        );
        if (existingUserFilter.length === 0) {
          const errorDes = `Existing User is not a member of the task`;
          errorsList.push({
            clientId: profile.tasks?.[0]?.client
              ? profile.tasks?.[0]?.client?.clientId
              : profile.tasks?.[0]?.clientGroup?.clientId,
            taskId: profile.tasks?.[0]?.taskNumber,
            taskName: profile.tasks?.[0]?.name,
            error: errorDes,
          });
          continue;
        }
        for (const task of profile.tasks) {
          task.members = task.members.filter((member) => member.id !== existingUser.id);
          task.members.push(newUser);
          if (task?.budgetedhours && task.budgetedhours !== 0) {
            const taskBudgetedHours = await BudgetedHours.find({
              where: { task },
              relations: ['user'],
            });
            for (const record of taskBudgetedHours) {
              if (record.user?.id === existingUser.id) {
                const previousBudgetedHours = record.budgetedHours;
                record.status = BudgetedHourStatus.INACTIVE;
                record.budgetedHours = 0;
                await record.save();
                let target = await BudgetedHours.findOne({
                  where: { task, user: newUser },
                });
                if (target) {
                  target.budgetedHours =
                    target.status === BudgetedHourStatus.ACTIVE
                      ? target.budgetedHours + previousBudgetedHours
                      : previousBudgetedHours;
                  target.status = BudgetedHourStatus.ACTIVE;
                  await target.save();
                } else {
                  target = new BudgetedHours();
                  target.status = BudgetedHourStatus.ACTIVE;
                  target.budgetedHours = previousBudgetedHours;
                  target.organization = user?.organization;
                  target.task = task;
                  target.user = newUser;
                  target.client = task?.client;
                  await target.save();
                }
              }
            }
          }

          task['bulkUpdate'] = true;
          validTaskIds.push(task.id);
        }
      }

      let validTasks: Task[] = [];
      for (const profile of recurringProfiles) {
        validTasks = [
          ...validTasks,
          ...profile.tasks.filter((task) => validTaskIds.includes(task.id)),
        ];
      }

      await Task.save(validTasks);

      return { validTaskIds, errorsList };
    } catch (err) {
      console.error('Error updating tasks:', err);
      throw new BadRequestException('Failed to update tasks');
    }
  }

  async getTaskActivity(userId, query) {
    const getTaskActivity = await createQueryBuilder(Activity, 'activity')
      .where('activity.type = :type', { type: ActivityType.TASK })
      .andWhere('activity.typeId = :id', { id: parseInt(query.typeId) })
      .andWhere('activity.action != :excludedAction', { excludedAction: 'Remark Added' });

    if (query?.fromDate && query?.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      getTaskActivity
        .andWhere('Date(activity.created_at) >= :startTime', { startTime })
        .andWhere('Date(activity.created_at) <= :endTime', { endTime });
    }

    if (query.category) {
      getTaskActivity.andWhere(`activity.action = :action`, { action: query.category });
    }

    getTaskActivity.orderBy('activity.id', 'DESC');

    const data = await getTaskActivity.getMany();

    return data;
  }
  async updateRemark(id: number, data: UpdateRemarksDto) {
    const activity = await Activity.findOne(id);
    activity.remarkType = data?.remarkType;
    activity.remarks = data?.remarks;
    await activity.save();
    return activity;
  }
  // async getUdinTasks(userId:number,query:any){
  //   let user = await User.findOne({
  //     where: { id: userId },
  //     relations: ['organization', 'role', 'role.permissions'],
  //   });

  //   let allTasks = user.role.permissions.find(
  //     (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
  //   );

  //   let assignedTasks = user.role.permissions.find(
  //     (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
  //   );

  //   let all = allTasks ? true : false;
  //   let assigned = !all && assignedTasks ? true : false;

  //   let tasks = createQueryBuilder(Task, 'task')
  //     .select([
  //       'task.id',
  //       'task.taskNumber',
  //       'task.name',
  //       'task.udinDate',
  //       'task.udinNumber'
  //     ])
  //     .leftJoinAndSelect('task.members', 'members')
  //     .leftJoinAndSelect('task.taskLeader', 'taskLeader')
  //     .leftJoinAndSelect('members.imageStorage', 'imageStorage')
  //     .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
  //     .leftJoinAndSelect('task.client', 'client')
  //     .leftJoinAndSelect('task.udinUser','udinUser')
  //     .leftJoin('task.user', 'user');

  //   tasks
  //     .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
  //     .leftJoinAndSelect('approvalProcedures.approval', 'approval')
  //     .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
  //     .leftJoinAndSelect('approvalLevels.user', 'approvalLevelsUsers');

  //   tasks.where('task.organization.id = :organization', { organization: user.organization.id })
  //   .andWhere('task.isUdin = :isUdin', { isUdin: true })

  //   if(query.selectedTaskType === 'task'){
  //     tasks.andWhere('task.parentTask IS NULL');
  //   }

  //   if(query.selectedTaskType === 'sub-task'){
  //     tasks.andWhere('task.parentTask IS NOT NULL');
  //   }

  //   if (assigned) {
  //     tasks.andWhere(
  //       new Brackets((qb) => {
  //         qb.where('approvalLevelsUsers.id = :userId AND task.status = :statusApproval', {
  //           userId,
  //           statusApproval: TaskStatusEnum.UNDER_REVIEW,
  //         })
  //         .orWhere(qb => {
  //           const subQuery = qb.subQuery()
  //             .select('task_id')
  //             .from('task_members_user', 'taskMembers')
  //             .where('taskMembers.user_id= :userId')
  //             .getQuery();
  //           qb.where(`task.id IN ${subQuery}`);
  //         })
  //         .orWhere('taskLeader.id = :userId', { userId });
  //       }),
  //     );
  //   }

  //   tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
  //     recurringStatus: TaskRecurringStatus.CREATED,
  //   });

  //   tasks
  //     .andWhere(
  //       new Brackets((qb) =>
  //         qb
  //           .where('task.status IN (:...statuses)', {
  //             statuses: [
  //               TaskStatusEnum.TODO,
  //               TaskStatusEnum.IN_PROGRESS,
  //               TaskStatusEnum.ON_HOLD,
  //               TaskStatusEnum.UNDER_REVIEW,
  //             ],
  //           })
  //           .orWhere('task.status = :completedStatus', {
  //             completedStatus: TaskStatusEnum.COMPLETED,
  //           })
  //           .andWhere('task.updatedAt >= :todayDate', {
  //             todayDate: moment().subtract(15, 'days').toDate(),
  //           }),
  //       ),
  //     )

  //     // .addOrderBy('task.createdDate', 'DESC')
  //     .addOrderBy('task.id', 'DESC');

  //     if (query.assignee) {
  //       tasks.andWhere('udinUser.id in (:...assignee)', {
  //         assignee: query.assignee,
  //       });
  //   }

  //   if (query.search) {
  //     tasks.andWhere(
  //       `(
  //         task.name like :search or
  //         task.taskNumber like :search or
  //         client.displayName like :search or
  //         task.udinNumber like :search
  //       )`,
  //       { search: '%' + query.search + '%' },
  //     );
  //   }

  //   this.filterByDate({
  //     query,
  //     tasks,
  //     entityKey: 'udinDate',
  //     dateFilterKey: DateFilterKeys.CREATED_ON,
  //   });

  //     if (query.offset) {
  //       tasks.skip(query.offset);
  //     }

  //     if (query.limit) {
  //       tasks.take(query.limit);
  //     }

  //     let result = await tasks.getManyAndCount();

  //     return result;
  // }

  async getAllStatusTasks(userId, query) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });
    const completedDays = organizationPreferences?.taskPreferences?.['completedDays'] || 15;

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.status',
        'task.recurringStatus',
        'task.createdAt',
        'task.approvalStatus',
        'task.recurring',
        'task.billable',
        'task.createdDate',
        'task.paymentStatus',
        'task.feeAmount',
      ])
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoin('task.user', 'user');

    tasks
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('approvalLevels.user', 'approvalLevelsUsers')
      .leftJoin('task.service', 'service')
      .where('task.organization.id = :organization', { organization: user.organization.id });

    if (!query.subTasks) {
      tasks.andWhere('task.parentTask IS NULL');
    }
    if (query?.mainTaks && query.mainTaks) {
      tasks.andWhere('task.parentTask IS NOT NULL');
    }

    if (assigned) {
      const subQuery = tasks
        .subQuery()
        .select('taskMembers.task_id')
        .from('task_members_user', 'taskMembers')
        .where('taskMembers.user_id = :userId')
        .getQuery();

      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('approvalLevelsUsers.id = :userId AND task.status = :statusApproval', {
            userId,
            statusApproval: TaskStatusEnum.UNDER_REVIEW,
          })
            .orWhere(`task.id IN ${subQuery}`, { userId })
            .orWhere('taskLeader.id = :userId', { userId });
        }),
      );
    }

    tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    });

    tasks
      .andWhere(
        new Brackets(
          (qb) =>
            qb
              .where('task.status IN (:...statuses)', {
                statuses: [
                  TaskStatusEnum.TODO,
                  TaskStatusEnum.IN_PROGRESS,
                  TaskStatusEnum.ON_HOLD,
                  TaskStatusEnum.UNDER_REVIEW,
                ],
              })
              .orWhere('task.status = :completedStatus', {
                completedStatus: TaskStatusEnum.COMPLETED,
              }),
          // .andWhere('task.updatedAt >= :todayDate', {
          //   todayDate: moment().subtract(parseInt(completedDays), 'days').toDate(),
          // }),
        ),
      )

      .addOrderBy('task.createdDate', 'DESC')
      .addOrderBy('task.id', 'DESC');

    if (query.completedOn && !query.createdOn) {
      tasks.andWhere(`task.status=:status`, { status: TaskStatusEnum.COMPLETED });
    }

    if (query.client) {
      tasks.andWhere('task.client.id = :client', {
        client: query.client,
      });
    }

    if (query.clientGroup) {
      tasks.andWhere('task.clientGroup.id = :clientGroup', {
        clientGroup: query.clientGroup,
      });
    }

    if (query.assignee) {
      const subQuery = tasks
        .subQuery()
        .select('taskMembers.task_id')
        .from('task_members_user', 'taskMembers')
        .where('taskMembers.user_id IN (:...assignee)', { assignee: query.assignee })
        .getQuery();

      tasks.andWhere(`task.id IN ${subQuery}`);
    }

    if (query.taskLeader) {
      tasks.andWhere('taskLeader.id in (:...taskLeader)', {
        taskLeader: query.taskLeader,
      });
    }

    if (query?.createdBy?.includes('Automatically')) {
      tasks.andWhere('task.recurring is true');
    }

    if (query.createdBy) {
      if (query.createdBy.includes('Automatically')) {
        query.createdBy = query.createdBy.filter((item) => item !== 'Automatically');
        if (query.createdBy.length) {
          tasks.andWhere('user.id in (:...createdBy)', {
            createdBy: query.createdBy,
          });
        }
      } else {
        tasks.andWhere('user.id in (:...createdBy)', {
          createdBy: query.createdBy,
        });
      }
    }

    let dueStatuses: any[] = query.status?.reduce((acc, status) => {
      if (status.endsWith('_overdue')) {
        acc.push(status.replace('_overdue', ''));
      }
      return acc;
    }, []);

    let nonDueStatuses: any[] = query.status?.filter((status) => !status.endsWith('_overdue'));
    tasks.andWhere(
      new Brackets((qb) => {
        let hasAddedCondition = false;
        let complitedStatus = false;
        if (nonDueStatuses?.length) {
          if (nonDueStatuses.includes(TaskStatusEnum.COMPLETED)) {
            nonDueStatuses = nonDueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
            complitedStatus = true;
          }
          if (nonDueStatuses?.length) {
            qb.where('task.status IN (:...nonDueStatuses) AND task.dueDate >= :todayDate', {
              nonDueStatuses: nonDueStatuses,
              todayDate: moment().format('YYYY-MM-DD').toString(),
            });
          }

          if (complitedStatus) {
            qb.andWhere(
              'task.status=:comStatus AND Date(task.dueDate) >= Date(task.statusUpdatedAt)',
              {
                comStatus: TaskStatusEnum.COMPLETED,
              },
            );
          }
          hasAddedCondition = true;
        }

        let dueComplited = false;
        if (dueStatuses?.length) {
          if (dueStatuses.includes(TaskStatusEnum.COMPLETED)) {
            dueStatuses = dueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
            dueComplited = true;
          }

          if (hasAddedCondition) {
            if (dueStatuses?.length) {
              qb.orWhere('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
                dueStatuses: dueStatuses,
                todayDatee: moment().format('YYYY-MM-DD').toString(),
              });
            }

            if (dueComplited) {
              qb.orWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
                dueComplited: TaskStatusEnum.COMPLETED,
              });
            }
          } else {
            if (dueStatuses?.length) {
              qb.where('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
                dueStatuses: dueStatuses,
                todayDatee: moment().format('YYYY-MM-DD').toString(),
              });
            }

            if (dueComplited) {
              qb.andWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
                dueComplited: TaskStatusEnum.COMPLETED,
              });
            }
          }
        }
      }),
    );

    // const serviceId = JSON.parse(query?.serviceDetails);
    // if (serviceId?.id) {
    //   tasks.andWhere('service.id = :serviceId', {
    //     serviceId: serviceId?.id,
    //   });
    // }

    if (query?.billingType?.length) {
      tasks.andWhere('task.paymentStatus in (:...paymentStatus)', {
        paymentStatus: query.billingType,
      });
    }

    if (query?.billable?.length) {
      let billableType = [];
      if (query?.billable?.includes('billable')) {
        billableType.push(true);
      }
      if (query?.billable?.includes('nonbillable')) {
        billableType.push(false);
      }
      tasks.andWhere('task.billable in (:...billable)', {
        billable: billableType,
      });
    }

    if (query.priority) {
      tasks.andWhere('task.priority in (:...priority)', {
        priority: query.priority,
      });
    }

    if (query.financialYear) {
      tasks.andWhere('task.financialYear in (:...financialYear)', {
        financialYear: query.financialYear,
      });
    }

    if (query.tags) {
      tasks.andWhere('labels.id in (:...labels)', {
        labels: query.tags,
      });
    }

    if (query.completedBy) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` (select
          user_id from task_status ts
          where ts.task_id = task.id
          and ts.status = "completed"
          order by ts.created_at desc
          limit 1) in (:...completedBy)`,
        { completedBy: query.completedBy },
      );
    }

    if (query.search) {
      tasks.andWhere(
        `(
          task.name like :search or
          task.taskNumber like :search or
          client.displayName like :search or
          clientGroup.displayName like :search
        )`,
        { search: '%' + query.search + '%' },
      );
    }

    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (query.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    if (query?.removeCompleted) {
      tasks.andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      });
    }

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'expectedCompletionDate',
      dateFilterKey: DateFilterKeys.EXPECTED_COMPLETION_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'statusUpdatedAt',
      dateFilterKey: DateFilterKeys.COMPLETED_ON,
    });

    if (query.offset) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      data: result[0],
    };
  }

  async getAllStatusTasksCopy(userId, query) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.status',
        'task.recurringStatus',
        'task.createdAt',
        'task.approvalStatus',
        'task.recurring',
        'task.billable',
        'task.createdDate',
        'task.paymentStatus',
        'task.feeAmount',
      ])
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoin('task.user', 'user');

    tasks
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('approvalLevels.user', 'approvalLevelsUsers')
      .leftJoin('task.service', 'service')
      .where('task.organization.id = :organization', { organization: user.organization.id });

    if (!query.subTasks) {
      tasks.andWhere('task.parentTask IS NULL');
    }
    if (query?.mainTaks && query.mainTaks) {
      tasks.andWhere('task.parentTask IS NOT NULL');
    }

    tasks.andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    });

    if (query?.serviceId) {
      tasks.andWhere('service.id = :serviceId', { serviceId: query?.serviceId });
    }

    if (query?.status) {
      tasks.andWhere('task.status = :status', { status: query.status });
    }

    if (query?.taskType) {
      if (query.taskType === 'recurring') {
        tasks.andWhere('task.recurring is true');
      }
      if (query.taskType === 'non_recurring') {
        tasks.andWhere('task.recurring is false');
      }
    }

    if (query?.billable) {
      if (query.billable === 'billableTasks') {
        tasks.andWhere('task.billable is true');
      }
      if (query.billable === 'nonBillableTasks') {
        tasks.andWhere('task.billable is false');
      }
    }

    if (query.priority) {
      tasks.andWhere('task.priority = :priority', { priority: query.priority });
    }

    if (query?.billingType) {
      if (query?.billingType === 'billedtasks') {
        tasks.andWhere('task.paymentStatus = :paymentStatus', {
          paymentStatus: PaymentStatusEnum.BILLED,
        });
      } else if (query?.billingType === 'unbilledTasks') {
        tasks.andWhere('task.paymentStatus = :paymentStatus', {
          paymentStatus: PaymentStatusEnum.UNBILLED,
        });
      }
    }

    if (query.offset) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      data: result[0],
    };
  }
}
